# Database Configuration
MONGODB_URI=mongodb://localhost:27017/sari-sari-store
MONGODB_DB_NAME=sari-sari-store

# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=Sari-Sari Store Management System
APP_VERSION=1.0.0
APP_DESCRIPTION=Professional Sari-Sari Store Management System

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-refresh-secret-change-this-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# API Configuration
API_URL=http://localhost:3000/api
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpeg,jpg,png,gif,webp
UPLOAD_DIR=uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Sari-Sari Store

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Cache Configuration (Optional - Redis)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Development Tools
ANALYZE=false
DISABLE_ESLINT=false
DISABLE_TYPE_CHECK=false

# Production Configuration
NEXT_TELEMETRY_DISABLED=1
