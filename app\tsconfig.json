{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": false, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/hooks/*": ["./src/hooks/*"], "@/styles/*": ["./src/styles/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "coverage", "dist"]}