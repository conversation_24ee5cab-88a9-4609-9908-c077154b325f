import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define transports
const transports = [
  // Console transport for development
  new winston.transports.Console({
    level: level(),
    format: logFormat,
  }),
];

// Add file transports for production
if (process.env.NODE_ENV === 'production') {
  // Error log file
  transports.push(
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'error.log'),
      level: 'error',
      format: fileLogFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );

  // Combined log file
  transports.push(
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'combined.log'),
      format: fileLogFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileLogFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Enhanced logging functions with context
export class Logger {
  private context: string;

  constructor(context: string = 'App') {
    this.context = context;
  }

  private formatMessage(message: string, meta?: any): string {
    const contextMsg = `[${this.context}] ${message}`;
    return meta ? `${contextMsg} ${JSON.stringify(meta)}` : contextMsg;
  }

  error(message: string, error?: Error | any, meta?: any): void {
    if (error instanceof Error) {
      logger.error(this.formatMessage(message, { ...meta, stack: error.stack }));
    } else {
      logger.error(this.formatMessage(message, { error, ...meta }));
    }
  }

  warn(message: string, meta?: any): void {
    logger.warn(this.formatMessage(message, meta));
  }

  info(message: string, meta?: any): void {
    logger.info(this.formatMessage(message, meta));
  }

  http(message: string, meta?: any): void {
    logger.http(this.formatMessage(message, meta));
  }

  debug(message: string, meta?: any): void {
    logger.debug(this.formatMessage(message, meta));
  }

  // Performance logging
  time(label: string): void {
    console.time(`[${this.context}] ${label}`);
  }

  timeEnd(label: string): void {
    console.timeEnd(`[${this.context}] ${label}`);
  }

  // API request logging
  apiRequest(method: string, url: string, statusCode: number, duration: number, meta?: any): void {
    const message = `${method} ${url} ${statusCode} - ${duration}ms`;
    if (statusCode >= 400) {
      this.error(message, meta);
    } else if (statusCode >= 300) {
      this.warn(message, meta);
    } else {
      this.http(message, meta);
    }
  }

  // Database operation logging
  dbOperation(operation: string, collection: string, duration: number, meta?: any): void {
    const message = `DB ${operation} on ${collection} - ${duration}ms`;
    this.debug(message, meta);
  }

  // Authentication logging
  authEvent(event: string, userId?: string, meta?: any): void {
    const message = `Auth: ${event}${userId ? ` for user ${userId}` : ''}`;
    this.info(message, meta);
  }

  // Business logic logging
  businessEvent(event: string, meta?: any): void {
    this.info(`Business: ${event}`, meta);
  }

  // Security logging
  securityEvent(event: string, severity: 'low' | 'medium' | 'high' = 'medium', meta?: any): void {
    const message = `Security [${severity.toUpperCase()}]: ${event}`;
    if (severity === 'high') {
      this.error(message, meta);
    } else if (severity === 'medium') {
      this.warn(message, meta);
    } else {
      this.info(message, meta);
    }
  }
}

// Create default logger instances
export const appLogger = new Logger('App');
export const apiLogger = new Logger('API');
export const dbLogger = new Logger('Database');
export const authLogger = new Logger('Auth');
export const securityLogger = new Logger('Security');

// Export the main logger for direct use
export default logger;

// Utility function to create contextual loggers
export function createLogger(context: string): Logger {
  return new Logger(context);
}

// Error logging middleware helper
export function logError(error: Error, context: string = 'Unknown', meta?: any): void {
  const contextLogger = new Logger(context);
  contextLogger.error(error.message, error, meta);
}

// Performance monitoring helper
export function withPerformanceLogging<T extends (...args: any[]) => any>(
  fn: T,
  context: string,
  operation: string
): T {
  return ((...args: any[]) => {
    const logger = new Logger(context);
    const startTime = Date.now();
    
    try {
      const result = fn(...args);
      
      // Handle async functions
      if (result instanceof Promise) {
        return result
          .then((res) => {
            const duration = Date.now() - startTime;
            logger.debug(`${operation} completed`, { duration, args });
            return res;
          })
          .catch((error) => {
            const duration = Date.now() - startTime;
            logger.error(`${operation} failed`, error, { duration, args });
            throw error;
          });
      }
      
      // Handle sync functions
      const duration = Date.now() - startTime;
      logger.debug(`${operation} completed`, { duration, args });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`${operation} failed`, error, { duration, args });
      throw error;
    }
  }) as T;
}

// Request ID generator for tracing
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Log levels for external use
export { levels as logLevels };
