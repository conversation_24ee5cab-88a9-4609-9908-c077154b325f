(()=>{var e={};e.id=168,e.ids=[168],e.modules={841:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(6037),a=r.n(s);let i=new s.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});i.index({customerName:1}),i.index({isPaid:1}),i.index({dateOfDebt:-1}),i.index({customerName:1,isPaid:1}),i.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),i.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),i.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let n=a().models.CustomerDebt||a().model("CustomerDebt",i)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2520:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var a=r(6559),i=r(8088),n=r(7719),o=r(2190),u=r(5745),c=r(841);async function d(e){try{await (0,u.A)();let{searchParams:t}=new URL(e.url),r=t.get("customer"),s=t.get("isPaid"),a=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"10"),n=(a-1)*i,d={};r&&(d.customerName={$regex:r,$options:"i"}),null!=s&&(d.isPaid="true"===s);let p=await c.A.find(d).sort({dateOfDebt:-1}).skip(n).limit(i),l=await c.A.countDocuments(d);return o.NextResponse.json({success:!0,data:p,pagination:{page:a,limit:i,total:l,pages:Math.ceil(l/i)}})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to fetch debts"},{status:500})}}async function p(e){try{await (0,u.A)();let{customerName:t,productName:r,productPrice:s,quantity:a,dateOfDebt:i,notes:n}=await e.json();if(!t||!r||!s||!a)return o.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(s<=0||a<=0)return o.NextResponse.json({success:!1,error:"Price and quantity must be positive"},{status:400});let d=new c.A({customerName:t.trim(),productName:r.trim(),productPrice:parseFloat(s),quantity:parseInt(a),dateOfDebt:i?new Date(i):new Date,notes:n||"",isPaid:!1});return await d.save(),o.NextResponse.json({success:!0,data:d,message:"Debt created successfully"},{status:201})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to create debt"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debts/route",pathname:"/api/debts",filename:"route",bundlePath:"app/api/debts/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=l;function b(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(6037),a=r.n(s);let i=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(i,{bufferCommands:!1}).then(()=>a().connection));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(2520));module.exports=s})();