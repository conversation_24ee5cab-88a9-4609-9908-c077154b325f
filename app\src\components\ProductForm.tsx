'use client';

import React, { useState, useEffect } from 'react';
import { X, Save } from 'lucide-react';
import { Product, PRODUCT_CATEGORIES } from '@/types';

interface ProductFormProps {
  product?: Product | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function ProductForm({
  product,
  onSubmit,
  onCancel,
}: ProductFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    image: '',
    netWeight: '',
    price: '',
    stockQuantity: '',
    category: 'snacks' as const,
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        image: product.image || '',
        netWeight: product.netWeight,
        price: product.price.toString(),
        stockQuantity: product.stockQuantity.toString(),
        category: product.category as any,
      });
    }
  }, [product]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.netWeight.trim()) {
      newErrors.netWeight = 'Net weight is required';
    }

    if (!formData.price || parseFloat(formData.price) < 0) {
      newErrors.price = 'Valid price is required';
    }

    if (!formData.stockQuantity || parseInt(formData.stockQuantity) < 0) {
      newErrors.stockQuantity = 'Valid stock quantity is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      const url = product ? `/api/products/${product._id}` : '/api/products';
      const method = product ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          stockQuantity: parseInt(formData.stockQuantity),
        }),
      });

      const data = await response.json();

      if (data.success) {
        onSubmit();
      } else {
        alert(data.error || 'Failed to save product');
      }
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Error saving product');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4'>
      <div className='max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl'>
        <div className='flex items-center justify-between border-b p-6'>
          <h2 className='text-xl font-semibold text-gray-900'>
            {product ? 'Edit Product' : 'Add New Product'}
          </h2>
          <button
            onClick={onCancel}
            className='text-gray-400 transition-colors hover:text-gray-600'
          >
            <X className='h-6 w-6' />
          </button>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4 p-6'>
          {/* Product Name */}
          <div>
            <label
              htmlFor='name'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Product Name *
            </label>
            <input
              type='text'
              id='name'
              name='name'
              value={formData.name}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Enter product name'
            />
            {errors.name && (
              <p className='mt-1 text-sm text-red-500'>{errors.name}</p>
            )}
          </div>

          {/* Product Image */}
          <div>
            <label
              htmlFor='image'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Product Image URL
            </label>
            <input
              type='url'
              id='image'
              name='image'
              value={formData.image}
              onChange={handleChange}
              className='w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500'
              placeholder='https://example.com/image.jpg'
            />
          </div>

          {/* Net Weight */}
          <div>
            <label
              htmlFor='netWeight'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Net Weight *
            </label>
            <input
              type='text'
              id='netWeight'
              name='netWeight'
              value={formData.netWeight}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.netWeight ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='e.g., 100g, 1L, 500ml'
            />
            {errors.netWeight && (
              <p className='mt-1 text-sm text-red-500'>{errors.netWeight}</p>
            )}
          </div>

          {/* Price */}
          <div>
            <label
              htmlFor='price'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Price (PHP) *
            </label>
            <input
              type='number'
              id='price'
              name='price'
              value={formData.price}
              onChange={handleChange}
              step='0.01'
              min='0'
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.price ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='0.00'
            />
            {errors.price && (
              <p className='mt-1 text-sm text-red-500'>{errors.price}</p>
            )}
          </div>

          {/* Stock Quantity */}
          <div>
            <label
              htmlFor='stockQuantity'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Stock Quantity *
            </label>
            <input
              type='number'
              id='stockQuantity'
              name='stockQuantity'
              value={formData.stockQuantity}
              onChange={handleChange}
              min='0'
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.stockQuantity ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='0'
            />
            {errors.stockQuantity && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.stockQuantity}
              </p>
            )}
          </div>

          {/* Category */}
          <div>
            <label
              htmlFor='category'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Category *
            </label>
            <select
              id='category'
              name='category'
              value={formData.category}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.category ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              {PRODUCT_CATEGORIES.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className='mt-1 text-sm text-red-500'>{errors.category}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className='flex gap-3 pt-4'>
            <button
              type='button'
              onClick={onCancel}
              className='flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50'
            >
              Cancel
            </button>
            <button
              type='submit'
              disabled={loading}
              className='flex flex-1 items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:opacity-50'
            >
              {loading ? (
                <div className='h-4 w-4 animate-spin rounded-full border-b-2 border-white'></div>
              ) : (
                <>
                  <Save className='h-4 w-4' />
                  {product ? 'Update' : 'Create'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
