import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import {
  successResponse,
  createPaginatedResponse,
  asyncHandler,
  validateRequest,
  ApiError,
  ConflictError
} from '@/lib/api-helpers';
import {
  productQuerySchema,
  createProductSchema,
  type ProductQuery,
  type CreateProductInput
} from '@/lib/validations';
import {
  standardApiMiddleware,
  withDatabase,
  withValidation,
  withCache,
  withPerformanceMonitoring,
  type RequestContext
} from '@/middleware/api-middleware';
import { cache, CacheKeys, CacheTTL } from '@/lib/cache';
import { apiLogger } from '@/lib/logger';

// GET /api/products - Get all products with professional error handling and caching
const getProductsHandler = asyncHandler(async (request: NextRequest, context: RequestContext) => {
  const url = new URL(request.url);
  const queryParams = Object.fromEntries(url.searchParams.entries());

  // Validate query parameters
  const validatedQuery = validateRequest<ProductQuery>(productQuerySchema, queryParams);

  const {
    page = 1,
    limit = 10,
    category,
    search,
    lowStock,
    outOfStock
  } = validatedQuery;

  const skip = (page - 1) * limit;

  // Build MongoDB query
  const query: any = {};

  if (category && category !== 'all') {
    query.category = category;
  }

  if (search) {
    query.name = { $regex: search, $options: 'i' };
  }

  if (lowStock) {
    query.stockQuantity = { $lte: 5 };
  }

  if (outOfStock) {
    query.stockQuantity = 0;
  }

  apiLogger.debug('Fetching products with query', { query, page, limit });

  // Execute database queries with performance monitoring
  const startTime = Date.now();

  const [products, total] = await Promise.all([
    Product.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(), // Use lean() for better performance
    Product.countDocuments(query)
  ]);

  const queryDuration = Date.now() - startTime;
  apiLogger.dbOperation('find', 'products', queryDuration, {
    resultCount: products.length,
    totalCount: total
  });

  return createPaginatedResponse(
    products,
    page,
    limit,
    total,
    `Retrieved ${products.length} products`
  );
});

export const GET = standardApiMiddleware(
  withDatabase(
    withPerformanceMonitoring()(
      withCache(
        (request) => {
          const url = new URL(request.url);
          const queryString = url.searchParams.toString();
          return CacheKeys.products.list(queryString);
        },
        CacheTTL.MEDIUM
      )(getProductsHandler)
    )
  )
);

// POST /api/products - Create a new product with professional validation and error handling
const createProductHandler = asyncHandler(async (request: NextRequest, context: RequestContext) => {
  const body = await request.json();

  // Validate request body
  const validatedData = validateRequest<CreateProductInput>(createProductSchema, body);

  const { name, image, netWeight, price, stockQuantity, category } = validatedData;

  apiLogger.info('Creating new product', { name, category, price });

  // Check if product with same name already exists (case-insensitive)
  const existingProduct = await Product.findOne({
    name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
  });

  if (existingProduct) {
    throw new ConflictError('A product with this name already exists');
  }

  // Create new product
  const productData = {
    name: name.trim(),
    image: image || '',
    netWeight: netWeight.trim(),
    price,
    stockQuantity,
    category,
  };

  const startTime = Date.now();
  const product = new Product(productData);
  await product.save();
  const saveDuration = Date.now() - startTime;

  apiLogger.dbOperation('create', 'products', saveDuration, { productId: product._id });
  apiLogger.businessEvent('Product created', {
    productId: product._id,
    name: product.name,
    category: product.category,
    price: product.price
  });

  // Invalidate related cache entries
  cache.invalidatePattern('products:');
  cache.invalidatePattern('dashboard:');

  return successResponse(
    product,
    'Product created successfully',
    undefined,
    201
  );
});

export const POST = standardApiMiddleware(
  withDatabase(
    withPerformanceMonitoring()(
      createProductHandler
    )
  )
);
