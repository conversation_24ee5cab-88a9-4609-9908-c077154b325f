import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import CustomerDebt from '@/lib/models/CustomerDebt';

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET() {
  try {
    await connectDB();

    // Get product statistics
    const productStats = await Product.aggregate([
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          lowStockProducts: {
            $sum: {
              $cond: [{ $lte: ['$stockQuantity', 5] }, 1, 0],
            },
          },
          totalStockValue: {
            $sum: { $multiply: ['$price', '$stockQuantity'] },
          },
        },
      },
    ]);

    // Get debt statistics
    const debtStats = await CustomerDebt.aggregate([
      {
        $group: {
          _id: null,
          totalCustomers: { $addToSet: '$customerName' },
          totalDebts: { $sum: 1 },
          totalUnpaidDebts: {
            $sum: {
              $cond: [{ $eq: ['$isPaid', false] }, 1, 0],
            },
          },
          totalDebtAmount: { $sum: '$totalAmount' },
          totalUnpaidAmount: {
            $sum: {
              $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0],
            },
          },
        },
      },
      {
        $project: {
          totalCustomers: { $size: '$totalCustomers' },
          totalDebts: 1,
          totalUnpaidDebts: 1,
          totalDebtAmount: 1,
          totalUnpaidAmount: 1,
          _id: 0,
        },
      },
    ]);

    // Get recent debts
    const recentDebts = await CustomerDebt.find(
      {},
      'customerName productName totalAmount dateOfDebt isPaid'
    )
      .sort({ dateOfDebt: -1 })
      .limit(5)
      .lean();

    // Get low stock products
    const lowStockProducts = await Product.find(
      { stockQuantity: { $lte: 5 } },
      'name stockQuantity price category'
    )
      .sort({ stockQuantity: 1 })
      .limit(5)
      .lean();

    // Get top customers by debt amount
    const topCustomers = await CustomerDebt.aggregate([
      { $match: { isPaid: false } },
      {
        $group: {
          _id: '$customerName',
          totalUnpaid: { $sum: '$totalAmount' },
          debtCount: { $sum: 1 },
        },
      },
      {
        $project: {
          customerName: '$_id',
          totalUnpaid: 1,
          debtCount: 1,
          _id: 0,
        },
      },
      { $sort: { totalUnpaid: -1 } },
      { $limit: 5 },
    ]);

    const stats = {
      products: productStats[0] || {
        totalProducts: 0,
        lowStockProducts: 0,
        totalStockValue: 0,
      },
      debts: debtStats[0] || {
        totalCustomers: 0,
        totalDebts: 0,
        totalUnpaidDebts: 0,
        totalDebtAmount: 0,
        totalUnpaidAmount: 0,
      },
      recentDebts,
      lowStockProducts,
      topCustomers,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
