import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { apiLogger, createLogger } from '@/lib/logger';
import { 
  errorResponse, 
  validationErrorResponse, 
  getRequestId, 
  getClientIP,
  checkRateLimit,
  setCorsHeaders,
  setSecurityHeaders,
  ApiError,
  RateLimitError
} from '@/lib/api-helpers';

// Request context interface
export interface RequestContext {
  requestId: string;
  clientIP: string;
  userAgent: string;
  startTime: number;
  method: string;
  url: string;
}

// Middleware options interface
export interface MiddlewareOptions {
  enableLogging?: boolean;
  enableRateLimit?: boolean;
  enableCors?: boolean;
  enableSecurity?: boolean;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  allowedMethods?: string[];
  requireAuth?: boolean;
}

// Default middleware options
const defaultOptions: MiddlewareOptions = {
  enableLogging: true,
  enableRateLimit: true,
  enableCors: true,
  enableSecurity: true,
  rateLimit: {
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
  },
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  requireAuth: false,
};

/**
 * Create API middleware with specified options
 */
export function createApiMiddleware(options: Partial<MiddlewareOptions> = {}) {
  const config = { ...defaultOptions, ...options };
  
  return function middleware(
    handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      const startTime = Date.now();
      const requestId = getRequestId(request);
      const clientIP = getClientIP(request);
      const userAgent = request.headers.get('user-agent') || 'unknown';
      
      const context: RequestContext = {
        requestId,
        clientIP,
        userAgent,
        startTime,
        method: request.method,
        url: request.url,
      };

      // Create contextual logger
      const logger = createLogger(`API-${requestId.slice(-8)}`);

      try {
        // Log incoming request
        if (config.enableLogging) {
          logger.http(`${request.method} ${request.url}`, {
            clientIP,
            userAgent,
            requestId,
          });
        }

        // Method validation
        if (config.allowedMethods && !config.allowedMethods.includes(request.method)) {
          throw new ApiError(
            `Method ${request.method} not allowed`,
            405,
            'METHOD_NOT_ALLOWED'
          );
        }

        // Rate limiting
        if (config.enableRateLimit && config.rateLimit) {
          const rateLimitKey = `${clientIP}:${request.method}:${new URL(request.url).pathname}`;
          const isAllowed = checkRateLimit(
            rateLimitKey,
            config.rateLimit.maxRequests,
            config.rateLimit.windowMs
          );
          
          if (!isAllowed) {
            throw new RateLimitError('Too many requests');
          }
        }

        // Handle OPTIONS request for CORS
        if (request.method === 'OPTIONS') {
          let response = new NextResponse(null, { status: 200 });
          
          if (config.enableCors) {
            response = setCorsHeaders(response);
          }
          
          if (config.enableSecurity) {
            response = setSecurityHeaders(response);
          }
          
          return response;
        }

        // Execute the handler
        let response = await handler(request, context);

        // Add CORS headers
        if (config.enableCors) {
          response = setCorsHeaders(response);
        }

        // Add security headers
        if (config.enableSecurity) {
          response = setSecurityHeaders(response);
        }

        // Add request ID header
        response.headers.set('X-Request-ID', requestId);

        // Log successful response
        if (config.enableLogging) {
          const duration = Date.now() - startTime;
          logger.apiRequest(
            request.method,
            request.url,
            response.status,
            duration,
            { requestId }
          );
        }

        return response;

      } catch (error) {
        const duration = Date.now() - startTime;
        
        // Handle different error types
        if (error instanceof ZodError) {
          logger.warn('Validation error', { error: error.errors, requestId, duration });
          return validationErrorResponse(error);
        }
        
        if (error instanceof ApiError) {
          logger.error(`API Error: ${error.message}`, error, { 
            requestId, 
            duration,
            statusCode: error.statusCode 
          });
          return errorResponse(error);
        }

        // Log unexpected errors
        logger.error('Unexpected API error', error, { requestId, duration });
        
        // Return generic error response
        return errorResponse(
          new ApiError('Internal server error', 500, 'INTERNAL_ERROR')
        );
      }
    };
  };
}

/**
 * Database connection middleware
 */
export function withDatabase<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const logger = createLogger('Database');
    
    try {
      // Import connectDB dynamically to avoid circular dependencies
      const { default: connectDB } = await import('@/lib/mongodb');
      
      const startTime = Date.now();
      await connectDB();
      const duration = Date.now() - startTime;
      
      logger.dbOperation('connect', 'mongodb', duration);
      
      return await handler(...args);
    } catch (error) {
      logger.error('Database connection failed', error);
      throw new ApiError('Database connection failed', 500, 'DATABASE_ERROR');
    }
  };
}

/**
 * Validation middleware
 */
export function withValidation<T>(schema: any) {
  return function (
    handler: (request: NextRequest, validatedData: T, context: RequestContext) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, context: RequestContext): Promise<NextResponse> => {
      try {
        let data: any;
        
        if (request.method === 'GET' || request.method === 'DELETE') {
          // For GET/DELETE, validate query parameters
          const url = new URL(request.url);
          data = Object.fromEntries(url.searchParams.entries());
        } else {
          // For POST/PUT/PATCH, validate request body
          data = await request.json();
        }
        
        const validatedData = schema.parse(data);
        return await handler(request, validatedData, context);
      } catch (error) {
        if (error instanceof ZodError) {
          return validationErrorResponse(error);
        }
        throw error;
      }
    };
  };
}

/**
 * Caching middleware
 */
export function withCache(
  keyGenerator: (request: NextRequest) => string,
  ttl: number = 300000 // 5 minutes default
) {
  return function (
    handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, context: RequestContext): Promise<NextResponse> => {
      // Only cache GET requests
      if (request.method !== 'GET') {
        return await handler(request, context);
      }

      const { cache } = await import('@/lib/cache');
      const cacheKey = keyGenerator(request);
      
      // Try to get from cache
      const cached = cache.get(cacheKey);
      if (cached) {
        const logger = createLogger('Cache');
        logger.debug(`Cache hit for key: ${cacheKey}`);
        
        return new NextResponse(JSON.stringify(cached), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'X-Cache': 'HIT',
            'X-Request-ID': context.requestId,
          },
        });
      }

      // Execute handler and cache result
      const response = await handler(request, context);
      
      if (response.status === 200) {
        try {
          const responseData = await response.json();
          cache.set(cacheKey, responseData, ttl);
          
          const logger = createLogger('Cache');
          logger.debug(`Cache set for key: ${cacheKey}`);
          
          return new NextResponse(JSON.stringify(responseData), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'X-Cache': 'MISS',
              'X-Request-ID': context.requestId,
            },
          });
        } catch (error) {
          // If response is not JSON, return as-is
          return response;
        }
      }

      return response;
    };
  };
}

/**
 * Performance monitoring middleware
 */
export function withPerformanceMonitoring() {
  return function (
    handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, context: RequestContext): Promise<NextResponse> => {
      const startTime = Date.now();
      const logger = createLogger('Performance');
      
      try {
        const response = await handler(request, context);
        const duration = Date.now() - startTime;
        
        // Log slow requests (> 1 second)
        if (duration > 1000) {
          logger.warn(`Slow API request detected`, {
            method: request.method,
            url: request.url,
            duration,
            requestId: context.requestId,
          });
        }
        
        // Add performance headers
        response.headers.set('X-Response-Time', `${duration}ms`);
        
        return response;
      } catch (error) {
        const duration = Date.now() - startTime;
        logger.error(`API request failed`, error, {
          method: request.method,
          url: request.url,
          duration,
          requestId: context.requestId,
        });
        throw error;
      }
    };
  };
}

// Export commonly used middleware combinations
export const standardApiMiddleware = createApiMiddleware();
export const publicApiMiddleware = createApiMiddleware({
  enableRateLimit: true,
  rateLimit: { maxRequests: 200, windowMs: 15 * 60 * 1000 },
});
export const restrictedApiMiddleware = createApiMiddleware({
  enableRateLimit: true,
  rateLimit: { maxRequests: 50, windowMs: 15 * 60 * 1000 },
  requireAuth: true,
});
