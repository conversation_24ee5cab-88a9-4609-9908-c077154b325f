{"name": "sari-sari-store", "version": "1.0.0", "description": "Professional Sari-Sari Store Management System built with Next.js 15, TypeScript, and Tailwind CSS", "private": true, "author": "Professional Developer", "license": "MIT", "keywords": ["sari-sari", "store", "inventory", "debt-management", "nextjs", "typescript", "tailwindcss"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "clsx": "^2.0.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "next": "^15.0.3", "nodemailer": "^6.9.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "sharp": "^0.32.6", "tailwind-merge": "^2.0.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^15.0.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-next": "^15.0.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "rimraf": "^5.0.5", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}