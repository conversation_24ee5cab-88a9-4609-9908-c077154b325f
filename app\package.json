{"name": "sari-sari-store", "version": "1.0.0", "description": "Professional Sari-Sari Store Management System built with Next.js 15, TypeScript, and Tailwind CSS", "private": true, "author": "Professional Developer", "license": "MIT", "keywords": ["sari-sari", "store", "inventory", "debt-management", "nextjs", "typescript", "tailwindcss"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out coverage"}, "dependencies": {"clsx": "^2.0.0", "lucide-react": "^0.294.0", "mongoose": "^8.0.3", "next": "^15.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^15.0.3", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}