import mongoose, { Schema, Document } from 'mongoose';
import { CustomerDebt as ICustomerDebt } from '../types';

export interface CustomerDebtDocument
  extends Omit<ICustomerDebt, '_id'>,
    Document {}

const CustomerDebtSchema = new Schema<CustomerDebtDocument>(
  {
    customerName: {
      type: String,
      required: [true, 'Customer name is required'],
      trim: true,
      maxlength: [100, 'Customer name cannot exceed 100 characters'],
    },
    productName: {
      type: String,
      required: [true, 'Product name is required'],
      trim: true,
      maxlength: [100, 'Product name cannot exceed 100 characters'],
    },
    productPrice: {
      type: Number,
      required: [true, 'Product price is required'],
      min: [0, 'Product price cannot be negative'],
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [1, 'Quantity must be at least 1'],
      validate: {
        validator: function (value: number) {
          return Number.isInteger(value) && value > 0;
        },
        message: 'Quantity must be a positive integer',
      },
    },
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required'],
      min: [0, 'Total amount cannot be negative'],
    },
    dateOfDebt: {
      type: Date,
      required: [true, 'Date of debt is required'],
      default: Date.now,
    },
    isPaid: {
      type: Boolean,
      default: false,
    },
    paidDate: {
      type: Date,
      default: null,
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, 'Notes cannot exceed 500 characters'],
      default: '',
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
CustomerDebtSchema.index({ customerName: 1 });
CustomerDebtSchema.index({ isPaid: 1 });
CustomerDebtSchema.index({ dateOfDebt: -1 });
CustomerDebtSchema.index({ customerName: 1, isPaid: 1 });

// Virtual for days since debt was created
CustomerDebtSchema.virtual('daysSinceDebt').get(function () {
  const now = new Date();
  const debtDate = new Date(this.dateOfDebt);
  const diffTime = Math.abs(now.getTime() - debtDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to calculate total amount and handle paid date
CustomerDebtSchema.pre('save', function (next) {
  // Calculate total amount
  this.totalAmount = this.productPrice * this.quantity;

  // Set paid date when marking as paid
  if (this.isPaid && !this.paidDate) {
    this.paidDate = new Date();
  }

  // Clear paid date when marking as unpaid
  if (!this.isPaid && this.paidDate) {
    this.paidDate = undefined;
  }

  next();
});

// Static method to get debt summary by customer
CustomerDebtSchema.statics.getDebtSummaryByCustomer = async function (
  customerName: string
) {
  const debts = await this.find({ customerName }).sort({ dateOfDebt: -1 });

  const totalDebt = debts.reduce(
    (sum: number, debt: any) => sum + debt.totalAmount,
    0
  );
  const totalUnpaid = debts
    .filter((debt: any) => !debt.isPaid)
    .reduce((sum: number, debt: any) => sum + debt.totalAmount, 0);

  return {
    customerName,
    totalDebt,
    totalUnpaid,
    debtCount: debts.length,
    unpaidCount: debts.filter((debt: any) => !debt.isPaid).length,
    debts,
  };
};

export default mongoose.models.CustomerDebt ||
  mongoose.model<CustomerDebtDocument>('CustomerDebt', CustomerDebtSchema);
