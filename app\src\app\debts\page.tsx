'use client';

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Users,
  DollarSign,
  Calendar,
  XCircle,
} from 'lucide-react';
import { CustomerDebt, DebtSummary } from '@/types';
import DebtForm from '@/components/DebtForm';
import DebtCard from '@/components/DebtCard';
import CustomerDebtSummary from '@/components/CustomerDebtSummary';
import Navigation from '@/components/Navigation';

export default function DebtsPage() {
  const [debts, setDebts] = useState<CustomerDebt[]>([]);
  const [customerSummaries, setCustomerSummaries] = useState<DebtSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState<'list' | 'summary'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [overallStats, setOverallStats] = useState({
    totalCustomers: 0,
    totalDebts: 0,
    totalUnpaidDebts: 0,
    totalDebtAmount: 0,
    totalUnpaidAmount: 0,
  });

  const fetchDebts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { customer: searchTerm }),
        ...(filterStatus !== 'all' && {
          isPaid: filterStatus === 'paid' ? 'true' : 'false',
        }),
      });

      const response = await fetch(`/api/debts?${params}`);
      const data = await response.json();

      if (data.success) {
        setDebts(data.data);
        setTotalPages(data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching debts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDebtSummary = async () => {
    try {
      const response = await fetch('/api/debts/summary');
      const data = await response.json();

      if (data.success) {
        setCustomerSummaries(data.data.customerSummaries);
        setOverallStats(data.data.overallStats);
      }
    } catch (error) {
      console.error('Error fetching debt summary:', error);
    }
  };

  useEffect(() => {
    if (viewMode === 'list') {
      fetchDebts();
    } else {
      fetchDebtSummary();
    }
  }, [viewMode, currentPage, searchTerm, filterStatus]);

  const handleAddDebt = () => {
    setEditingDebt(null);
    setShowForm(true);
  };

  const handleEditDebt = (debt: CustomerDebt) => {
    setEditingDebt(debt);
    setShowForm(true);
  };

  const handleDeleteDebt = async (debtId: string) => {
    if (!confirm('Are you sure you want to delete this debt record?')) return;

    try {
      const response = await fetch(`/api/debts/${debtId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        if (viewMode === 'list') {
          fetchDebts();
        } else {
          fetchDebtSummary();
        }
      } else {
        alert('Failed to delete debt record');
      }
    } catch (error) {
      console.error('Error deleting debt:', error);
      alert('Error deleting debt record');
    }
  };

  const handleTogglePayment = async (debt: CustomerDebt) => {
    try {
      const response = await fetch(`/api/debts/${debt._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...debt,
          isPaid: !debt.isPaid,
        }),
      });

      if (response.ok) {
        if (viewMode === 'list') {
          fetchDebts();
        } else {
          fetchDebtSummary();
        }
      } else {
        alert('Failed to update payment status');
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      alert('Error updating payment status');
    }
  };

  const handleFormSubmit = () => {
    setShowForm(false);
    setEditingDebt(null);
    if (viewMode === 'list') {
      fetchDebts();
    } else {
      fetchDebtSummary();
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    if (viewMode === 'list') {
      fetchDebts();
    } else {
      fetchDebtSummary();
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <Navigation />

      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='mb-2 text-3xl font-bold text-gray-900'>
              Customer Debts (Utang)
            </h1>
            <p className='text-gray-600'>
              Track and manage customer debt records
            </p>
          </div>
          <button
            onClick={handleAddDebt}
            className='flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white transition-colors hover:bg-green-700'
          >
            <Plus className='h-5 w-5' />
            Add Debt
          </button>
        </div>

        {/* Stats Cards */}
        <div className='mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <Users className='mr-3 h-8 w-8 text-blue-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Total Customers
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {overallStats.totalCustomers}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <Calendar className='mr-3 h-8 w-8 text-orange-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>Total Debts</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {overallStats.totalDebts}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <XCircle className='mr-3 h-8 w-8 text-red-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Unpaid Debts
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {overallStats.totalUnpaidDebts}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <DollarSign className='mr-3 h-8 w-8 text-green-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Unpaid Amount
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  ₱{overallStats.totalUnpaidAmount.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* View Mode Toggle and Filters */}
        <div className='mb-8 rounded-lg bg-white p-6 shadow-sm'>
          <div className='flex flex-col items-start justify-between gap-4 md:flex-row md:items-center'>
            <div className='flex gap-2'>
              <button
                onClick={() => setViewMode('list')}
                className={`rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Debt List
              </button>
              <button
                onClick={() => setViewMode('summary')}
                className={`rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                  viewMode === 'summary'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Customer Summary
              </button>
            </div>

            <div className='flex flex-1 flex-col gap-4 md:max-w-md md:flex-row'>
              <form onSubmit={handleSearch} className='flex-1'>
                <div className='relative'>
                  <Search className='absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400' />
                  <input
                    type='text'
                    placeholder='Search customers...'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className='w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500'
                  />
                </div>
              </form>

              {viewMode === 'list' && (
                <div className='flex items-center gap-2'>
                  <Filter className='h-5 w-5 text-gray-400' />
                  <select
                    value={filterStatus}
                    onChange={e => {
                      setFilterStatus(e.target.value);
                      setCurrentPage(1);
                    }}
                    className='rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='all'>All Status</option>
                    <option value='unpaid'>Unpaid</option>
                    <option value='paid'>Paid</option>
                  </select>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
          </div>
        ) : viewMode === 'list' ? (
          <>
            {debts.length === 0 ? (
              <div className='py-12 text-center'>
                <Users className='mx-auto mb-4 h-16 w-16 text-gray-400' />
                <h3 className='mb-2 text-xl font-semibold text-gray-900'>
                  No debt records found
                </h3>
                <p className='mb-6 text-gray-600'>
                  Start tracking customer debts
                </p>
                <button
                  onClick={handleAddDebt}
                  className='inline-flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white hover:bg-green-700'
                >
                  <Plus className='h-5 w-5' />
                  Add Debt Record
                </button>
              </div>
            ) : (
              <>
                <div className='mb-8 grid gap-4'>
                  {debts.map(debt => (
                    <DebtCard
                      key={debt._id}
                      debt={debt}
                      onEdit={handleEditDebt}
                      onDelete={handleDeleteDebt}
                      onTogglePayment={handleTogglePayment}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className='flex justify-center gap-2'>
                    <button
                      onClick={() =>
                        setCurrentPage(prev => Math.max(prev - 1, 1))
                      }
                      disabled={currentPage === 1}
                      className='rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
                    >
                      Previous
                    </button>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                      page => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`rounded-lg border px-4 py-2 ${
                            currentPage === page
                              ? 'border-blue-600 bg-blue-600 text-white'
                              : 'border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      )
                    )}

                    <button
                      onClick={() =>
                        setCurrentPage(prev => Math.min(prev + 1, totalPages))
                      }
                      disabled={currentPage === totalPages}
                      className='rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
                    >
                      Next
                    </button>
                  </div>
                )}
              </>
            )}
          </>
        ) : (
          <div className='grid gap-6'>
            {customerSummaries.length === 0 ? (
              <div className='py-12 text-center'>
                <Users className='mx-auto mb-4 h-16 w-16 text-gray-400' />
                <h3 className='mb-2 text-xl font-semibold text-gray-900'>
                  No customer debt records
                </h3>
                <p className='text-gray-600'>
                  Customer debt summaries will appear here
                </p>
              </div>
            ) : (
              customerSummaries.map(summary => (
                <CustomerDebtSummary
                  key={summary.customerName}
                  summary={summary}
                />
              ))
            )}
          </div>
        )}
      </div>

      {/* Debt Form Modal */}
      {showForm && (
        <DebtForm
          debt={editingDebt}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingDebt(null);
          }}
        />
      )}
    </div>
  );
}
