import React from 'react';
import { Loader2, Package, ShoppingCart, Users, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Loading spinner variants
export type SpinnerVariant = 'default' | 'dots' | 'pulse' | 'bounce' | 'fade';
export type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';

interface LoadingSpinnerProps {
  variant?: SpinnerVariant;
  size?: SpinnerSize;
  className?: string;
  color?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
};

export function LoadingSpinner({ 
  variant = 'default', 
  size = 'md', 
  className,
  color = 'text-blue-600'
}: LoadingSpinnerProps) {
  const baseClasses = cn(sizeClasses[size], color, className);

  switch (variant) {
    case 'dots':
      return (
        <div className={cn('flex space-x-1', className)}>
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                'rounded-full bg-current animate-pulse',
                size === 'sm' ? 'w-1 h-1' : 
                size === 'md' ? 'w-2 h-2' : 
                size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                color
              )}
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1.4s',
              }}
            />
          ))}
        </div>
      );

    case 'pulse':
      return (
        <div className={cn(baseClasses, 'rounded-full bg-current animate-pulse')} />
      );

    case 'bounce':
      return (
        <div className={cn('flex space-x-1', className)}>
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                'rounded-full bg-current animate-bounce',
                size === 'sm' ? 'w-2 h-2' : 
                size === 'md' ? 'w-3 h-3' : 
                size === 'lg' ? 'w-4 h-4' : 'w-5 h-5',
                color
              )}
              style={{
                animationDelay: `${i * 0.1}s`,
              }}
            />
          ))}
        </div>
      );

    case 'fade':
      return (
        <div className={cn('relative', className)}>
          {[0, 1, 2, 3, 4, 5, 6, 7].map((i) => (
            <div
              key={i}
              className={cn(
                'absolute rounded-full bg-current',
                size === 'sm' ? 'w-1 h-1' : 
                size === 'md' ? 'w-2 h-2' : 
                size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                color
              )}
              style={{
                transform: `rotate(${i * 45}deg) translate(${
                  size === 'sm' ? '8px' : 
                  size === 'md' ? '12px' : 
                  size === 'lg' ? '16px' : '20px'
                }, 0)`,
                animation: `fade 1.2s linear infinite`,
                animationDelay: `${i * 0.15}s`,
              }}
            />
          ))}
        </div>
      );

    default:
      return <Loader2 className={cn(baseClasses, 'animate-spin')} />;
  }
}

// Full page loading component
interface PageLoadingProps {
  message?: string;
  icon?: React.ReactNode;
  className?: string;
}

export function PageLoading({ 
  message = 'Loading...', 
  icon,
  className 
}: PageLoadingProps) {
  return (
    <div className={cn(
      'min-h-screen bg-gray-50 flex items-center justify-center px-4',
      className
    )}>
      <div className="text-center">
        <div className="mb-4">
          {icon || <LoadingSpinner size="xl" />}
        </div>
        <p className="text-gray-600 text-lg">{message}</p>
      </div>
    </div>
  );
}

// Card loading skeleton
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('bg-white rounded-lg shadow-sm p-6 animate-pulse', className)}>
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
      <div className="space-y-3">
        <div className="h-3 bg-gray-200 rounded"></div>
        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        <div className="h-3 bg-gray-200 rounded w-4/6"></div>
      </div>
    </div>
  );
}

// Table loading skeleton
export function TableSkeleton({ 
  rows = 5, 
  columns = 4,
  className 
}: { 
  rows?: number; 
  columns?: number;
  className?: string;
}) {
  return (
    <div className={cn('bg-white rounded-lg shadow-sm overflow-hidden', className)}>
      <div className="animate-pulse">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
        
        {/* Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="px-6 py-4 border-b border-gray-100">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Button loading state
interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export function LoadingButton({ 
  loading = false, 
  children, 
  loadingText,
  disabled,
  className,
  ...props 
}: LoadingButtonProps) {
  return (
    <button
      {...props}
      disabled={loading || disabled}
      className={cn(
        'relative inline-flex items-center justify-center',
        loading && 'cursor-not-allowed',
        className
      )}
    >
      {loading && (
        <LoadingSpinner 
          size="sm" 
          className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2" 
        />
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {loading && loadingText ? loadingText : children}
      </span>
    </button>
  );
}

// Context-specific loading components
export function ProductsLoading() {
  return (
    <PageLoading 
      message="Loading products..." 
      icon={<Package className="w-12 h-12 text-blue-600 animate-pulse" />}
    />
  );
}

export function DebtsLoading() {
  return (
    <PageLoading 
      message="Loading debt records..." 
      icon={<Users className="w-12 h-12 text-green-600 animate-pulse" />}
    />
  );
}

export function DashboardLoading() {
  return (
    <PageLoading 
      message="Loading dashboard..." 
      icon={<BarChart3 className="w-12 h-12 text-purple-600 animate-pulse" />}
    />
  );
}

// Grid loading skeleton
export function GridSkeleton({ 
  items = 8, 
  columns = 4,
  className 
}: { 
  items?: number; 
  columns?: number;
  className?: string;
}) {
  return (
    <div className={cn(
      'grid gap-6',
      columns === 2 && 'grid-cols-1 md:grid-cols-2',
      columns === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      columns === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      className
    )}>
      {Array.from({ length: items }).map((_, i) => (
        <CardSkeleton key={i} />
      ))}
    </div>
  );
}

// Inline loading component
export function InlineLoading({ 
  message = 'Loading...', 
  className 
}: { 
  message?: string; 
  className?: string;
}) {
  return (
    <div className={cn('flex items-center space-x-2 text-gray-600', className)}>
      <LoadingSpinner size="sm" />
      <span className="text-sm">{message}</span>
    </div>
  );
}

// Add CSS for fade animation
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fade {
      0%, 39%, 100% { opacity: 0; }
      40% { opacity: 1; }
    }
  `;
  document.head.appendChild(style);
}
