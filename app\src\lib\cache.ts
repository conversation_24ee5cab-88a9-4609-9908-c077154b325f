import { createLogger } from './logger';

const logger = createLogger('Cache');

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

// Cache statistics interface
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  size: number;
  hitRate: number;
}

// In-memory cache implementation
class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0,
    hitRate: 0,
  };
  private maxSize: number;
  private defaultTTL: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxSize: number = 1000, defaultTTL: number = 300000) { // 5 minutes default
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Start cleanup interval (every 5 minutes)
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 300000);
  }

  /**
   * Get value from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.size--;
      this.updateHitRate();
      return null;
    }

    // Update hit count and stats
    entry.hits++;
    this.stats.hits++;
    this.updateHitRate();
    
    logger.debug(`Cache hit for key: ${key}`, { hits: entry.hits });
    return entry.data;
  }

  /**
   * Set value in cache
   */
  set<T>(key: string, value: T, ttl?: number): void {
    const actualTTL = ttl || this.defaultTTL;
    
    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl: actualTTL,
      hits: 0,
    };

    const isUpdate = this.cache.has(key);
    this.cache.set(key, entry);
    
    if (!isUpdate) {
      this.stats.size++;
    }
    this.stats.sets++;
    
    logger.debug(`Cache set for key: ${key}`, { ttl: actualTTL });
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
      this.stats.size--;
      logger.debug(`Cache delete for key: ${key}`);
    }
    return deleted;
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.size--;
      return false;
    }
    
    return true;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.stats.size = 0;
    this.stats.deletes += size;
    logger.info('Cache cleared', { deletedEntries: size });
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }
    
    this.stats.size = this.cache.size;
    this.stats.deletes += cleaned;
    
    if (cleaned > 0) {
      logger.debug(`Cache cleanup completed`, { cleanedEntries: cleaned });
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();
    let leastHits = Infinity;
    
    for (const [key, entry] of this.cache.entries()) {
      // Prioritize by hits first, then by age
      if (entry.hits < leastHits || (entry.hits === leastHits && entry.timestamp < oldestTime)) {
        oldestKey = key;
        oldestTime = entry.timestamp;
        leastHits = entry.hits;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.size--;
      this.stats.deletes++;
      logger.debug(`Cache LRU eviction`, { evictedKey: oldestKey });
    }
  }

  /**
   * Update hit rate
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Destroy cache and cleanup
   */
  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.clear();
    logger.info('Cache destroyed');
  }
}

// Create global cache instance
const globalCache = new MemoryCache(1000, 300000); // 1000 entries, 5 minutes TTL

// Cache key generators
export const CacheKeys = {
  products: {
    list: (query: string) => `products:list:${query}`,
    detail: (id: string) => `products:detail:${id}`,
    stats: () => 'products:stats',
    categories: () => 'products:categories',
    lowStock: () => 'products:lowStock',
  },
  debts: {
    list: (query: string) => `debts:list:${query}`,
    detail: (id: string) => `debts:detail:${id}`,
    summary: () => 'debts:summary',
    customerSummary: (customer: string) => `debts:customer:${customer}`,
  },
  dashboard: {
    stats: () => 'dashboard:stats',
  },
};

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 60000,      // 1 minute
  MEDIUM: 300000,    // 5 minutes
  LONG: 900000,      // 15 minutes
  HOUR: 3600000,     // 1 hour
  DAY: 86400000,     // 24 hours
};

// High-level cache functions
export const cache = {
  /**
   * Get value from cache
   */
  get: <T>(key: string): T | null => {
    return globalCache.get<T>(key);
  },

  /**
   * Set value in cache
   */
  set: <T>(key: string, value: T, ttl?: number): void => {
    globalCache.set(key, value, ttl);
  },

  /**
   * Delete value from cache
   */
  delete: (key: string): boolean => {
    return globalCache.delete(key);
  },

  /**
   * Check if key exists
   */
  has: (key: string): boolean => {
    return globalCache.has(key);
  },

  /**
   * Clear all cache
   */
  clear: (): void => {
    globalCache.clear();
  },

  /**
   * Get cache statistics
   */
  stats: (): CacheStats => {
    return globalCache.getStats();
  },

  /**
   * Get or set pattern
   */
  getOrSet: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> => {
    const cached = globalCache.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await fetcher();
    globalCache.set(key, value, ttl);
    return value;
  },

  /**
   * Invalidate cache by pattern
   */
  invalidatePattern: (pattern: string): number => {
    const keys = globalCache.keys();
    let deleted = 0;
    
    for (const key of keys) {
      if (key.includes(pattern)) {
        globalCache.delete(key);
        deleted++;
      }
    }
    
    logger.debug(`Cache pattern invalidation`, { pattern, deletedKeys: deleted });
    return deleted;
  },

  /**
   * Warm up cache with data
   */
  warmUp: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<void> => {
    try {
      const value = await fetcher();
      globalCache.set(key, value, ttl);
      logger.debug(`Cache warmed up`, { key });
    } catch (error) {
      logger.error(`Cache warm up failed`, error, { key });
    }
  },
};

// Cache middleware for API routes
export function withCache<T>(
  key: string,
  ttl: number = CacheTTL.MEDIUM
) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = typeof key === 'function' ? key(...args) : key;
      
      // Try to get from cache
      const cached = cache.get<T>(cacheKey);
      if (cached !== null) {
        logger.debug(`Cache hit for method ${propertyName}`, { key: cacheKey });
        return cached;
      }

      // Execute original method
      const result = await method.apply(this, args);
      
      // Cache the result
      cache.set(cacheKey, result, ttl);
      logger.debug(`Cache set for method ${propertyName}`, { key: cacheKey });
      
      return result;
    };
  };
}

// Export cache instance and utilities
export { globalCache };
export default cache;
