(()=>{var e={};e.id=146,e.ids=[146],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(6037),n=r.n(s);let a=new s.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(e){return e>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({name:1}),a.index({category:1}),a.index({stockQuantity:1}),a.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),a.pre("save",function(e){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),e()});let i=n().models.Product||n().model("Product",a)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4236:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var n=r(6559),a=r(8088),i=r(7719),o=r(2190),c=r(5745),u=r(1253);async function d(e){try{await (0,c.A)();let{searchParams:t}=new URL(e.url),r=t.get("category"),s=t.get("search"),n=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),i=(n-1)*a,d={};r&&"all"!==r&&(d.category=r),s&&(d.name={$regex:s,$options:"i"});let p=await u.A.find(d).sort({createdAt:-1}).skip(i).limit(a),l=await u.A.countDocuments(d);return o.NextResponse.json({success:!0,data:p,pagination:{page:n,limit:a,total:l,pages:Math.ceil(l/a)}})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to fetch products"},{status:500})}}async function p(e){try{await (0,c.A)();let{name:t,image:r,netWeight:s,price:n,stockQuantity:a,category:i}=await e.json();if(!t||!s||!n||!i)return o.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(n<0||a<0)return o.NextResponse.json({success:!1,error:"Price and stock quantity must be non-negative"},{status:400});let d=new u.A({name:t,image:r||"",netWeight:s,price:parseFloat(n),stockQuantity:parseInt(a)||0,category:i});return await d.save(),o.NextResponse.json({success:!0,data:d,message:"Product created successfully"},{status:201})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to create product"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=l;function y(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(6037),n=r.n(s);let a=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;i||(i=global.mongoose={conn:null,promise:null});let o=async function(){if(i.conn)return i.conn;i.promise||(i.promise=n().connect(a,{bufferCommands:!1}).then(()=>n().connection));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(4236));module.exports=s})();