'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Package, Users } from 'lucide-react';

export default function Navigation() {
  const pathname = usePathname();

  const navItems = [
    {
      href: '/',
      label: 'Dashboard',
      icon: Home,
      active: pathname === '/',
    },
    {
      href: '/products',
      label: 'Products',
      icon: Package,
      active: pathname === '/products',
    },
    {
      href: '/debts',
      label: 'Customer Debts',
      icon: Users,
      active: pathname === '/debts',
    },
  ];

  return (
    <nav className='border-b bg-white shadow-sm'>
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center justify-between'>
          <Link href='/' className='flex items-center space-x-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 shadow-md'>
              <span className='text-lg font-bold text-white'>🏪</span>
            </div>
            <div className='hidden sm:block'>
              <span className='text-lg font-bold text-gray-900'>Sari-Sari Store</span>
              <p className='text-xs text-gray-500'>Admin Dashboard</p>
            </div>
          </Link>

          <div className='flex space-x-1'>
            {navItems.map(item => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200 ${
                    item.active
                      ? 'bg-blue-100 text-blue-700 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 hover:shadow-sm'
                  }`}
                >
                  <Icon className='h-4 w-4' />
                  <span className='hidden sm:inline'>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}
