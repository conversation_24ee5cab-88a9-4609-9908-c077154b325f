'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Package, Users } from 'lucide-react';

export default function Navigation() {
  const pathname = usePathname();

  const navItems = [
    {
      href: '/',
      label: 'Dashboard',
      icon: Home,
      active: pathname === '/',
    },
    {
      href: '/products',
      label: 'Products',
      icon: Package,
      active: pathname === '/products',
    },
    {
      href: '/debts',
      label: 'Customer Debts',
      icon: Users,
      active: pathname === '/debts',
    },
  ];

  return (
    <nav className='border-b bg-white shadow-sm'>
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center justify-between'>
          <Link href='/' className='flex items-center space-x-2'>
            <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600'>
              <span className='text-sm font-bold text-white'>SS</span>
            </div>
            <span className='font-semibold text-gray-900'>Sari-Sari Admin</span>
          </Link>

          <div className='flex space-x-1'>
            {navItems.map(item => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                    item.active
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <Icon className='h-4 w-4' />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}
