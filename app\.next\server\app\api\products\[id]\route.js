(()=>{var e={};e.id=856,e.ids=[856],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(6037),n=s.n(r);let o=new r.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(e){return e>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({name:1}),o.index({category:1}),o.index({stockQuantity:1}),o.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),o.pre("save",function(e){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),e()});let i=n().models.Product||n().model("Product",o)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(6037),n=s.n(r);let o=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;i||(i=global.mongoose={conn:null,promise:null});let a=async function(){if(i.conn)return i.conn;i.promise||(i.promise=n().connect(o,{bufferCommands:!1}).then(()=>n().connection));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9372:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{DELETE:()=>g,GET:()=>l,PUT:()=>m});var n=s(6559),o=s(8088),i=s(7719),a=s(2190),u=s(5745),c=s(1253),d=s(6037),p=s.n(d);async function l(e,{params:t}){try{if(await (0,u.A)(),!p().Types.ObjectId.isValid(t.id))return a.NextResponse.json({success:!1,error:"Invalid product ID"},{status:400});let e=await c.A.findById(t.id);if(!e)return a.NextResponse.json({success:!1,error:"Product not found"},{status:404});return a.NextResponse.json({success:!0,data:e})}catch(e){return a.NextResponse.json({success:!1,error:"Failed to fetch product"},{status:500})}}async function m(e,{params:t}){try{if(await (0,u.A)(),!p().Types.ObjectId.isValid(t.id))return a.NextResponse.json({success:!1,error:"Invalid product ID"},{status:400});let{name:s,image:r,netWeight:n,price:o,stockQuantity:i,category:d}=await e.json();if(!s||!n||!o||!d)return a.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(o<0||i<0)return a.NextResponse.json({success:!1,error:"Price and stock quantity must be non-negative"},{status:400});if(await c.A.findOne({name:{$regex:RegExp(`^${s}$`,"i")},_id:{$ne:t.id}}))return a.NextResponse.json({success:!1,error:"Another product with this name already exists"},{status:400});let l=await c.A.findByIdAndUpdate(t.id,{name:s,image:r||"",netWeight:n,price:parseFloat(o),stockQuantity:parseInt(i)||0,category:d},{new:!0,runValidators:!0});if(!l)return a.NextResponse.json({success:!1,error:"Product not found"},{status:404});return a.NextResponse.json({success:!0,data:l,message:"Product updated successfully"})}catch(e){return a.NextResponse.json({success:!1,error:"Failed to update product"},{status:500})}}async function g(e,{params:t}){try{if(await (0,u.A)(),!p().Types.ObjectId.isValid(t.id))return a.NextResponse.json({success:!1,error:"Invalid product ID"},{status:400});if(!await c.A.findByIdAndDelete(t.id))return a.NextResponse.json({success:!1,error:"Product not found"},{status:404});return a.NextResponse.json({success:!0,message:"Product deleted successfully"})}catch(e){return a.NextResponse.json({success:!1,error:"Failed to delete product"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:f,serverHooks:h}=x;function v(){return(0,i.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:f})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(9372));module.exports=r})();