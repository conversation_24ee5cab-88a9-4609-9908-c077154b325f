(()=>{var t={};t.id=694,t.ids=[694],t.modules={841:(t,e,a)=>{"use strict";a.d(e,{A:()=>s});var r=a(6037),i=a.n(r);let o=new r.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(t){return Number.isInteger(t)&&t>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({customerName:1}),o.index({isPaid:1}),o.index({dateOfDebt:-1}),o.index({customerName:1,isPaid:1}),o.virtual("daysSinceDebt").get(function(){let t=new Date,e=new Date(this.dateOfDebt);return Math.ceil(Math.abs(t.getTime()-e.getTime())/864e5)}),o.pre("save",function(t){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),t()}),o.statics.getDebtSummaryByCustomer=async function(t){let e=await this.find({customerName:t}).sort({dateOfDebt:-1});return{customerName:t,totalDebt:e.reduce((t,e)=>t+e.totalAmount,0),totalUnpaid:e.filter(t=>!t.isPaid).reduce((t,e)=>t+e.totalAmount,0),debtCount:e.length,unpaidCount:e.filter(t=>!t.isPaid).length,debts:e}};let s=i().models.CustomerDebt||i().model("CustomerDebt",o)},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(t,e,a)=>{"use strict";a.d(e,{A:()=>s});var r=a(6037),i=a.n(r);let o=new r.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(t){return t>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({name:1}),o.index({category:1}),o.index({stockQuantity:1}),o.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),o.pre("save",function(t){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),t()});let s=i().models.Product||i().model("Product",o)},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(t,e,a)=>{"use strict";a.d(e,{A:()=>n});var r=a(6037),i=a.n(r);let o=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let n=async function(){if(s.conn)return s.conn;s.promise||(s.promise=i().connect(o,{bufferCommands:!1}).then(()=>i().connection));try{s.conn=await s.promise}catch(t){throw s.promise=null,t}return s.conn}},6037:t=>{"use strict";t.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9838:(t,e,a)=>{"use strict";a.r(e),a.d(e,{patchFetch:()=>b,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var r={};a.r(r),a.d(r,{GET:()=>l});var i=a(6559),o=a(8088),s=a(7719),n=a(2190),u=a(5745),d=a(1253),c=a(841);async function l(){try{await (0,u.A)();let t=await d.A.aggregate([{$group:{_id:null,totalProducts:{$sum:1},lowStockProducts:{$sum:{$cond:[{$lte:["$stockQuantity",5]},1,0]}},totalStockValue:{$sum:{$multiply:["$price","$stockQuantity"]}}}}]),e=await c.A.aggregate([{$group:{_id:null,totalCustomers:{$addToSet:"$customerName"},totalDebts:{$sum:1},totalUnpaidDebts:{$sum:{$cond:[{$eq:["$isPaid",!1]},1,0]}},totalDebtAmount:{$sum:"$totalAmount"},totalUnpaidAmount:{$sum:{$cond:[{$eq:["$isPaid",!1]},"$totalAmount",0]}}}},{$project:{totalCustomers:{$size:"$totalCustomers"},totalDebts:1,totalUnpaidDebts:1,totalDebtAmount:1,totalUnpaidAmount:1,_id:0}}]),a=await c.A.find({},"customerName productName totalAmount dateOfDebt isPaid").sort({dateOfDebt:-1}).limit(5).lean(),r=await d.A.find({stockQuantity:{$lte:5}},"name stockQuantity price category").sort({stockQuantity:1}).limit(5).lean(),i=await c.A.aggregate([{$match:{isPaid:!1}},{$group:{_id:"$customerName",totalUnpaid:{$sum:"$totalAmount"},debtCount:{$sum:1}}},{$project:{customerName:"$_id",totalUnpaid:1,debtCount:1,_id:0}},{$sort:{totalUnpaid:-1}},{$limit:5}]),o={products:t[0]||{totalProducts:0,lowStockProducts:0,totalStockValue:0},debts:e[0]||{totalCustomers:0,totalDebts:0,totalUnpaidDebts:0,totalDebtAmount:0,totalUnpaidAmount:0},recentDebts:a,lowStockProducts:r,topCustomers:i};return n.NextResponse.json({success:!0,data:o})}catch(t){return n.NextResponse.json({success:!1,error:"Failed to fetch dashboard statistics"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:h}=m;function b(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}}};var e=require("../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[447,580],()=>a(9838));module.exports=r})();