/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n/* harmony import */ var _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CustomerDebt */ \"(rsc)/./src/lib/models/CustomerDebt.ts\");\n\n\n\n\n// GET /api/dashboard/stats - Get dashboard statistics\nasync function GET() {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get product statistics\n        const productStats = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: null,\n                    totalProducts: {\n                        $sum: 1\n                    },\n                    lowStockProducts: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $lte: [\n                                        '$stockQuantity',\n                                        5\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    totalStockValue: {\n                        $sum: {\n                            $multiply: [\n                                '$price',\n                                '$stockQuantity'\n                            ]\n                        }\n                    }\n                }\n            }\n        ]);\n        // Get debt statistics\n        const debtStats = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: null,\n                    totalCustomers: {\n                        $addToSet: '$customerName'\n                    },\n                    totalDebts: {\n                        $sum: 1\n                    },\n                    totalUnpaidDebts: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    totalDebtAmount: {\n                        $sum: '$totalAmount'\n                    },\n                    totalUnpaidAmount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                '$totalAmount',\n                                0\n                            ]\n                        }\n                    }\n                }\n            },\n            {\n                $project: {\n                    totalCustomers: {\n                        $size: '$totalCustomers'\n                    },\n                    totalDebts: 1,\n                    totalUnpaidDebts: 1,\n                    totalDebtAmount: 1,\n                    totalUnpaidAmount: 1,\n                    _id: 0\n                }\n            }\n        ]);\n        // Get recent debts\n        const recentDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({}, 'customerName productName totalAmount dateOfDebt isPaid').sort({\n            dateOfDebt: -1\n        }).limit(5).lean();\n        // Get low stock products\n        const lowStockProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            stockQuantity: {\n                $lte: 5\n            }\n        }, 'name stockQuantity price category').sort({\n            stockQuantity: 1\n        }).limit(5).lean();\n        // Get top customers by debt amount\n        const topCustomers = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].aggregate([\n            {\n                $match: {\n                    isPaid: false\n                }\n            },\n            {\n                $group: {\n                    _id: '$customerName',\n                    totalUnpaid: {\n                        $sum: '$totalAmount'\n                    },\n                    debtCount: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $project: {\n                    customerName: '$_id',\n                    totalUnpaid: 1,\n                    debtCount: 1,\n                    _id: 0\n                }\n            },\n            {\n                $sort: {\n                    totalUnpaid: -1\n                }\n            },\n            {\n                $limit: 5\n            }\n        ]);\n        const stats = {\n            products: productStats[0] || {\n                totalProducts: 0,\n                lowStockProducts: 0,\n                totalStockValue: 0\n            },\n            debts: debtStats[0] || {\n                totalCustomers: 0,\n                totalDebts: 0,\n                totalUnpaidDebts: 0,\n                totalDebtAmount: 0,\n                totalUnpaidAmount: 0\n            },\n            recentDebts,\n            lowStockProducts,\n            topCustomers\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats\n        });\n    } catch (error) {\n        console.error('Error fetching dashboard stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch dashboard statistics'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/CustomerDebt.ts":
/*!****************************************!*\
  !*** ./src/lib/models/CustomerDebt.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomerDebtSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    customerName: {\n        type: String,\n        required: [\n            true,\n            'Customer name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Customer name cannot exceed 100 characters'\n        ]\n    },\n    productName: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    productPrice: {\n        type: Number,\n        required: [\n            true,\n            'Product price is required'\n        ],\n        min: [\n            0,\n            'Product price cannot be negative'\n        ]\n    },\n    quantity: {\n        type: Number,\n        required: [\n            true,\n            'Quantity is required'\n        ],\n        min: [\n            1,\n            'Quantity must be at least 1'\n        ],\n        validate: {\n            validator: function(value) {\n                return Number.isInteger(value) && value > 0;\n            },\n            message: 'Quantity must be a positive integer'\n        }\n    },\n    totalAmount: {\n        type: Number,\n        required: [\n            true,\n            'Total amount is required'\n        ],\n        min: [\n            0,\n            'Total amount cannot be negative'\n        ]\n    },\n    dateOfDebt: {\n        type: Date,\n        required: [\n            true,\n            'Date of debt is required'\n        ],\n        default: Date.now\n    },\n    isPaid: {\n        type: Boolean,\n        default: false\n    },\n    paidDate: {\n        type: Date,\n        default: null\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Notes cannot exceed 500 characters'\n        ],\n        default: ''\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nCustomerDebtSchema.index({\n    customerName: 1\n});\nCustomerDebtSchema.index({\n    isPaid: 1\n});\nCustomerDebtSchema.index({\n    dateOfDebt: -1\n});\nCustomerDebtSchema.index({\n    customerName: 1,\n    isPaid: 1\n});\n// Virtual for days since debt was created\nCustomerDebtSchema.virtual('daysSinceDebt').get(function() {\n    const now = new Date();\n    const debtDate = new Date(this.dateOfDebt);\n    const diffTime = Math.abs(now.getTime() - debtDate.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware to calculate total amount and handle paid date\nCustomerDebtSchema.pre('save', function(next) {\n    // Calculate total amount\n    this.totalAmount = this.productPrice * this.quantity;\n    // Set paid date when marking as paid\n    if (this.isPaid && !this.paidDate) {\n        this.paidDate = new Date();\n    }\n    // Clear paid date when marking as unpaid\n    if (!this.isPaid && this.paidDate) {\n        this.paidDate = undefined;\n    }\n    next();\n});\n// Static method to get debt summary by customer\nCustomerDebtSchema.statics.getDebtSummaryByCustomer = async function(customerName) {\n    const debts = await this.find({\n        customerName\n    }).sort({\n        dateOfDebt: -1\n    });\n    const totalDebt = debts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    const totalUnpaid = debts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    return {\n        customerName,\n        totalDebt,\n        totalUnpaid,\n        debtCount: debts.length,\n        unpaidCount: debts.filter((debt)=>!debt.isPaid).length,\n        debts\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CustomerDebt || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('CustomerDebt', CustomerDebtSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/CustomerDebt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    image: {\n        type: String,\n        trim: true,\n        default: ''\n    },\n    netWeight: {\n        type: String,\n        required: [\n            true,\n            'Net weight is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Net weight cannot exceed 50 characters'\n        ]\n    },\n    price: {\n        type: Number,\n        required: [\n            true,\n            'Price is required'\n        ],\n        min: [\n            0,\n            'Price cannot be negative'\n        ],\n        validate: {\n            validator: function(value) {\n                return value >= 0;\n            },\n            message: 'Price must be a positive number'\n        }\n    },\n    stockQuantity: {\n        type: Number,\n        required: [\n            true,\n            'Stock quantity is required'\n        ],\n        min: [\n            0,\n            'Stock quantity cannot be negative'\n        ],\n        default: 0\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: {\n            values: [\n                'snacks',\n                'canned goods',\n                'beverages',\n                'personal care',\n                'household',\n                'condiments',\n                'instant foods',\n                'dairy',\n                'frozen',\n                'others'\n            ],\n            message: 'Invalid category'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nProductSchema.index({\n    name: 1\n});\nProductSchema.index({\n    category: 1\n});\nProductSchema.index({\n    stockQuantity: 1\n});\n// Virtual for low stock indicator\nProductSchema.virtual('isLowStock').get(function() {\n    return this.stockQuantity <= 5;\n});\n// Pre-save middleware to ensure data consistency\nProductSchema.pre('save', function(next) {\n    if (this.price < 0) {\n        this.price = 0;\n    }\n    if (this.stockQuantity < 0) {\n        this.stockQuantity = 0;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Product', ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then(()=>{\n            return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection);\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();