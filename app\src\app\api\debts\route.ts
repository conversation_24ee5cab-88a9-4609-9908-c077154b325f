import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomerDebt from '@/lib/models/CustomerDebt';

// GET /api/debts - Get all debts
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const customer = searchParams.get('customer');
    const isPaid = searchParams.get('isPaid');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};

    if (customer) {
      query.customerName = { $regex: customer, $options: 'i' };
    }

    if (isPaid !== null && isPaid !== undefined) {
      query.isPaid = isPaid === 'true';
    }

    // Get debts with pagination
    const debts = await CustomerDebt.find(query)
      .sort({ dateOfDebt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await CustomerDebt.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: debts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching debts:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch debts' },
      { status: 500 }
    );
  }
}

// POST /api/debts - Create a new debt
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      customerName,
      productName,
      productPrice,
      quantity,
      dateOfDebt,
      notes,
    } = body;

    // Validation
    if (!customerName || !productName || !productPrice || !quantity) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (productPrice <= 0 || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Price and quantity must be positive' },
        { status: 400 }
      );
    }

    const debt = new CustomerDebt({
      customerName: customerName.trim(),
      productName: productName.trim(),
      productPrice: parseFloat(productPrice),
      quantity: parseInt(quantity),
      dateOfDebt: dateOfDebt ? new Date(dateOfDebt) : new Date(),
      notes: notes || '',
      isPaid: false,
    });

    await debt.save();

    return NextResponse.json(
      {
        success: true,
        data: debt,
        message: 'Debt created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating debt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create debt' },
      { status: 500 }
    );
  }
}
