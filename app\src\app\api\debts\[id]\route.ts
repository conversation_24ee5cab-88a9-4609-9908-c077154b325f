import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomerDebt from '@/lib/models/CustomerDebt';
import mongoose from 'mongoose';

// GET /api/debts/[id] - Get a single debt
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid debt ID' },
        { status: 400 }
      );
    }

    const debt = await CustomerDebt.findById(params.id);

    if (!debt) {
      return NextResponse.json(
        { success: false, error: 'Debt not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: debt,
    });
  } catch (error) {
    console.error('Error fetching debt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch debt' },
      { status: 500 }
    );
  }
}

// PUT /api/debts/[id] - Update a debt
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid debt ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      customerName,
      productName,
      productPrice,
      quantity,
      dateOfDebt,
      isPaid,
      notes,
    } = body;

    // Validation
    if (!customerName || !productName || !productPrice || !quantity) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (productPrice <= 0 || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Price and quantity must be positive' },
        { status: 400 }
      );
    }

    const updateData: any = {
      customerName: customerName.trim(),
      productName: productName.trim(),
      productPrice: parseFloat(productPrice),
      quantity: parseInt(quantity),
      dateOfDebt: dateOfDebt ? new Date(dateOfDebt) : new Date(),
      notes: notes || '',
      isPaid: Boolean(isPaid),
    };

    // Handle paid date
    if (isPaid && !updateData.paidDate) {
      updateData.paidDate = new Date();
    } else if (!isPaid) {
      updateData.paidDate = null;
    }

    const debt = await CustomerDebt.findByIdAndUpdate(params.id, updateData, {
      new: true,
      runValidators: true,
    });

    if (!debt) {
      return NextResponse.json(
        { success: false, error: 'Debt not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: debt,
      message: 'Debt updated successfully',
    });
  } catch (error) {
    console.error('Error updating debt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update debt' },
      { status: 500 }
    );
  }
}

// DELETE /api/debts/[id] - Delete a debt
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid debt ID' },
        { status: 400 }
      );
    }

    const debt = await CustomerDebt.findByIdAndDelete(params.id);

    if (!debt) {
      return NextResponse.json(
        { success: false, error: 'Debt not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Debt deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting debt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete debt' },
      { status: 500 }
    );
  }
}
