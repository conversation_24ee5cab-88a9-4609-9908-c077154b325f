'use client';

import React, { useState, useEffect } from 'react';
import { X, Save, Calculator } from 'lucide-react';
import { CustomerDebt } from '@/types';

interface DebtFormProps {
  debt?: CustomerDebt | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function DebtForm({ debt, onSubmit, onCancel }: DebtFormProps) {
  const [formData, setFormData] = useState({
    customerName: '',
    productName: '',
    productPrice: '',
    quantity: '',
    dateOfDebt: '',
    isPaid: false,
    notes: '',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (debt) {
      setFormData({
        customerName: debt.customerName,
        productName: debt.productName,
        productPrice: debt.productPrice.toString(),
        quantity: debt.quantity.toString(),
        dateOfDebt: debt.dateOfDebt
          ? new Date(debt.dateOfDebt).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
        isPaid: debt.isPaid,
        notes: debt.notes || '',
      });
    } else {
      // Set default date to today
      setFormData(prev => ({
        ...prev,
        dateOfDebt: new Date().toISOString().split('T')[0],
      }));
    }
  }, [debt]);

  const calculateTotal = () => {
    const price = parseFloat(formData.productPrice) || 0;
    const quantity = parseInt(formData.quantity) || 0;
    return price * quantity;
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.productName.trim()) {
      newErrors.productName = 'Product name is required';
    }

    if (!formData.productPrice || parseFloat(formData.productPrice) <= 0) {
      newErrors.productPrice = 'Valid product price is required';
    }

    if (!formData.quantity || parseInt(formData.quantity) <= 0) {
      newErrors.quantity = 'Valid quantity is required';
    }

    if (!formData.dateOfDebt) {
      newErrors.dateOfDebt = 'Date of debt is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      const url = debt ? `/api/debts/${debt._id}` : '/api/debts';
      const method = debt ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          productPrice: parseFloat(formData.productPrice),
          quantity: parseInt(formData.quantity),
        }),
      });

      const data = await response.json();

      if (data.success) {
        onSubmit();
      } else {
        alert(data.error || 'Failed to save debt record');
      }
    } catch (error) {
      console.error('Error saving debt:', error);
      alert('Error saving debt record');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4'>
      <div className='max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl'>
        <div className='flex items-center justify-between border-b p-6'>
          <h2 className='text-xl font-semibold text-gray-900'>
            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}
          </h2>
          <button
            onClick={onCancel}
            className='text-gray-400 transition-colors hover:text-gray-600'
          >
            <X className='h-6 w-6' />
          </button>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4 p-6'>
          {/* Customer Name */}
          <div>
            <label
              htmlFor='customerName'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Customer Name *
            </label>
            <input
              type='text'
              id='customerName'
              name='customerName'
              value={formData.customerName}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.customerName ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Enter customer name'
            />
            {errors.customerName && (
              <p className='mt-1 text-sm text-red-500'>{errors.customerName}</p>
            )}
          </div>

          {/* Product Name */}
          <div>
            <label
              htmlFor='productName'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Product Name *
            </label>
            <input
              type='text'
              id='productName'
              name='productName'
              value={formData.productName}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.productName ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Enter product name'
            />
            {errors.productName && (
              <p className='mt-1 text-sm text-red-500'>{errors.productName}</p>
            )}
          </div>

          {/* Product Price */}
          <div>
            <label
              htmlFor='productPrice'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Product Price (PHP) *
            </label>
            <input
              type='number'
              id='productPrice'
              name='productPrice'
              value={formData.productPrice}
              onChange={handleChange}
              step='0.01'
              min='0'
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.productPrice ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='0.00'
            />
            {errors.productPrice && (
              <p className='mt-1 text-sm text-red-500'>{errors.productPrice}</p>
            )}
          </div>

          {/* Quantity */}
          <div>
            <label
              htmlFor='quantity'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Quantity *
            </label>
            <input
              type='number'
              id='quantity'
              name='quantity'
              value={formData.quantity}
              onChange={handleChange}
              min='1'
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.quantity ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='1'
            />
            {errors.quantity && (
              <p className='mt-1 text-sm text-red-500'>{errors.quantity}</p>
            )}
          </div>

          {/* Total Amount Display */}
          {formData.productPrice && formData.quantity && (
            <div className='rounded-lg bg-blue-50 p-3'>
              <div className='flex items-center gap-2'>
                <Calculator className='h-5 w-5 text-blue-600' />
                <span className='text-sm font-medium text-blue-900'>
                  Total Amount: ₱{calculateTotal().toFixed(2)}
                </span>
              </div>
            </div>
          )}

          {/* Date of Debt */}
          <div>
            <label
              htmlFor='dateOfDebt'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Date of Debt *
            </label>
            <input
              type='date'
              id='dateOfDebt'
              name='dateOfDebt'
              value={formData.dateOfDebt}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${
                errors.dateOfDebt ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.dateOfDebt && (
              <p className='mt-1 text-sm text-red-500'>{errors.dateOfDebt}</p>
            )}
          </div>

          {/* Payment Status */}
          <div className='flex items-center'>
            <input
              type='checkbox'
              id='isPaid'
              name='isPaid'
              checked={formData.isPaid}
              onChange={handleChange}
              className='h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500'
            />
            <label
              htmlFor='isPaid'
              className='ml-2 block text-sm text-gray-900'
            >
              Mark as paid
            </label>
          </div>

          {/* Notes */}
          <div>
            <label
              htmlFor='notes'
              className='mb-1 block text-sm font-medium text-gray-700'
            >
              Notes (Optional)
            </label>
            <textarea
              id='notes'
              name='notes'
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className='w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500'
              placeholder='Additional notes about this debt...'
            />
          </div>

          {/* Form Actions */}
          <div className='flex gap-3 pt-4'>
            <button
              type='button'
              onClick={onCancel}
              className='flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50'
            >
              Cancel
            </button>
            <button
              type='submit'
              disabled={loading}
              className='flex flex-1 items-center justify-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700 disabled:opacity-50'
            >
              {loading ? (
                <div className='h-4 w-4 animate-spin rounded-full border-b-2 border-white'></div>
              ) : (
                <>
                  <Save className='h-4 w-4' />
                  {debt ? 'Update' : 'Create'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
