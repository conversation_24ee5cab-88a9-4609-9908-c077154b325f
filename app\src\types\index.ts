export interface Product {
  _id?: string;
  name: string;
  image?: string;
  netWeight: string;
  price: number;
  stockQuantity: number;
  category: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CustomerDebt {
  _id?: string;
  customerName: string;
  productName: string;
  productPrice: number;
  quantity: number;
  totalAmount: number;
  dateOfDebt: Date;
  isPaid: boolean;
  paidDate?: Date;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DebtSummary {
  customerName: string;
  totalDebt: number;
  totalUnpaid: number;
  debtCount: number;
  unpaidCount: number;
  debts: CustomerDebt[];
}

export interface DashboardStats {
  totalProducts: number;
  totalCustomers: number;
  totalDebts: number;
  totalUnpaidDebts: number;
  totalDebtAmount: number;
  totalUnpaidAmount: number;
}

export type ProductCategory =
  | 'snacks'
  | 'canned goods'
  | 'beverages'
  | 'personal care'
  | 'household'
  | 'condiments'
  | 'instant foods'
  | 'dairy'
  | 'frozen'
  | 'others';

export const PRODUCT_CATEGORIES: ProductCategory[] = [
  'snacks',
  'canned goods',
  'beverages',
  'personal care',
  'household',
  'condiments',
  'instant foods',
  'dairy',
  'frozen',
  'others',
];
