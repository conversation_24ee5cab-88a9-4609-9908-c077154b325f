// Simple API test script
// Run this after starting the development server to test API endpoints

const BASE_URL = 'http://localhost:3000/api';

async function testAPI() {
  console.log('🧪 Testing Sari-Sari Store API Endpoints...\n');

  try {
    // Test 1: Get dashboard stats
    console.log('1. Testing Dashboard Stats...');
    const statsResponse = await fetch(`${BASE_URL}/dashboard/stats`);
    const statsData = await statsResponse.json();
    console.log(
      '✅ Dashboard stats:',
      statsData.success ? 'SUCCESS' : 'FAILED'
    );

    // Test 2: Get products
    console.log('\n2. Testing Products API...');
    const productsResponse = await fetch(`${BASE_URL}/products`);
    const productsData = await productsResponse.json();
    console.log(
      '✅ Get products:',
      productsData.success ? 'SUCCESS' : 'FAILED'
    );

    // Test 3: Create a test product
    console.log('\n3. Testing Create Product...');
    const newProduct = {
      name: 'Test Product',
      netWeight: '100g',
      price: 25.5,
      stockQuantity: 10,
      category: 'snacks',
    };

    const createProductResponse = await fetch(`${BASE_URL}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newProduct),
    });
    const createProductData = await createProductResponse.json();
    console.log(
      '✅ Create product:',
      createProductData.success ? 'SUCCESS' : 'FAILED'
    );

    let productId = null;
    if (createProductData.success) {
      productId = createProductData.data._id;
      console.log('   Created product ID:', productId);
    }

    // Test 4: Get debts
    console.log('\n4. Testing Debts API...');
    const debtsResponse = await fetch(`${BASE_URL}/debts`);
    const debtsData = await debtsResponse.json();
    console.log('✅ Get debts:', debtsData.success ? 'SUCCESS' : 'FAILED');

    // Test 5: Create a test debt
    console.log('\n5. Testing Create Debt...');
    const newDebt = {
      customerName: 'Test Customer',
      productName: 'Test Product',
      productPrice: 25.5,
      quantity: 2,
      notes: 'Test debt record',
    };

    const createDebtResponse = await fetch(`${BASE_URL}/debts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newDebt),
    });
    const createDebtData = await createDebtResponse.json();
    console.log(
      '✅ Create debt:',
      createDebtData.success ? 'SUCCESS' : 'FAILED'
    );

    let debtId = null;
    if (createDebtData.success) {
      debtId = createDebtData.data._id;
      console.log('   Created debt ID:', debtId);
    }

    // Test 6: Get debt summary
    console.log('\n6. Testing Debt Summary...');
    const summaryResponse = await fetch(`${BASE_URL}/debts/summary`);
    const summaryData = await summaryResponse.json();
    console.log('✅ Debt summary:', summaryData.success ? 'SUCCESS' : 'FAILED');

    // Cleanup: Delete test records
    console.log('\n🧹 Cleaning up test data...');

    if (productId) {
      const deleteProductResponse = await fetch(
        `${BASE_URL}/products/${productId}`,
        {
          method: 'DELETE',
        }
      );
      const deleteProductData = await deleteProductResponse.json();
      console.log(
        '✅ Delete test product:',
        deleteProductData.success ? 'SUCCESS' : 'FAILED'
      );
    }

    if (debtId) {
      const deleteDebtResponse = await fetch(`${BASE_URL}/debts/${debtId}`, {
        method: 'DELETE',
      });
      const deleteDebtData = await deleteDebtResponse.json();
      console.log(
        '✅ Delete test debt:',
        deleteDebtData.success ? 'SUCCESS' : 'FAILED'
      );
    }

    console.log('\n🎉 API testing completed!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    console.log('\nMake sure:');
    console.log('1. MongoDB is running');
    console.log('2. Development server is running (npm run dev)');
    console.log('3. Server is accessible at http://localhost:3000');
  }
}

// Run the test
testAPI();
