import { envSchema, type EnvConfig } from './validations';
import { createLogger } from './logger';

const logger = createLogger('Config');

/**
 * Validate and parse environment variables
 */
function validateEnv(): EnvConfig {
  try {
    const env = {
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT,
      MONGODB_URI: process.env.MONGODB_URI,
      JWT_SECRET: process.env.JWT_SECRET,
      JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET,
      JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN,
      JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN,
      BCRYPT_ROUNDS: process.env.BCRYPT_ROUNDS,
      MAX_FILE_SIZE: process.env.MAX_FILE_SIZE,
      ALLOWED_FILE_TYPES: process.env.ALLOWED_FILE_TYPES,
      UPLOAD_DIR: process.env.UPLOAD_DIR,
      RATE_LIMIT_WINDOW_MS: process.env.RATE_LIMIT_WINDOW_MS,
      RATE_LIMIT_MAX_REQUESTS: process.env.RATE_LIMIT_MAX_REQUESTS,
      CORS_ORIGIN: process.env.CORS_ORIGIN,
      LOG_LEVEL: process.env.LOG_LEVEL,
    };

    const validatedEnv = envSchema.parse(env);
    
    logger.info('Environment variables validated successfully', {
      nodeEnv: validatedEnv.NODE_ENV,
      port: validatedEnv.PORT,
      logLevel: validatedEnv.LOG_LEVEL,
    });

    return validatedEnv;
  } catch (error) {
    logger.error('Environment validation failed', error);
    throw new Error('Invalid environment configuration');
  }
}

// Export validated configuration
export const config = validateEnv();

// Database configuration
export const dbConfig = {
  uri: config.MONGODB_URI,
  options: {
    maxPoolSize: 10,
    minPoolSize: 2,
    maxIdleTimeMS: 30000,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    bufferCommands: false,
    bufferMaxEntries: 0,
    retryWrites: true,
    retryReads: true,
    compressors: ['zlib'],
    monitorCommands: config.NODE_ENV === 'development',
  },
} as const;

// JWT configuration
export const jwtConfig = {
  secret: config.JWT_SECRET,
  refreshSecret: config.JWT_REFRESH_SECRET,
  expiresIn: config.JWT_EXPIRES_IN,
  refreshExpiresIn: config.JWT_REFRESH_EXPIRES_IN,
  algorithm: 'HS256' as const,
  issuer: 'sari-sari-store',
  audience: 'sari-sari-store-users',
} as const;

// Security configuration
export const securityConfig = {
  bcryptRounds: config.BCRYPT_ROUNDS,
  rateLimit: {
    windowMs: config.RATE_LIMIT_WINDOW_MS,
    maxRequests: config.RATE_LIMIT_MAX_REQUESTS,
  },
  cors: {
    origin: config.CORS_ORIGIN.split(',').map(origin => origin.trim()),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
  },
  headers: {
    contentTypeOptions: 'nosniff',
    frameOptions: 'DENY',
    xssProtection: '1; mode=block',
    referrerPolicy: 'strict-origin-when-cross-origin',
    permissionsPolicy: 'camera=(), microphone=(), geolocation=()',
  },
} as const;

// File upload configuration
export const uploadConfig = {
  maxFileSize: config.MAX_FILE_SIZE,
  allowedTypes: config.ALLOWED_FILE_TYPES.split(',').map(type => type.trim()),
  uploadDir: config.UPLOAD_DIR,
  imageFormats: ['jpeg', 'jpg', 'png', 'webp', 'gif'],
  maxImageWidth: 2048,
  maxImageHeight: 2048,
  imageQuality: 85,
} as const;

// Cache configuration
export const cacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 1000,
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  ttl: {
    short: 1 * 60 * 1000,      // 1 minute
    medium: 5 * 60 * 1000,     // 5 minutes
    long: 15 * 60 * 1000,      // 15 minutes
    hour: 60 * 60 * 1000,      // 1 hour
    day: 24 * 60 * 60 * 1000,  // 24 hours
  },
} as const;

// API configuration
export const apiConfig = {
  version: 'v1',
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  pagination: {
    defaultLimit: 10,
    maxLimit: 100,
  },
} as const;

// Logging configuration
export const loggingConfig = {
  level: config.LOG_LEVEL,
  maxFileSize: '20m',
  maxFiles: '14d',
  datePattern: 'YYYY-MM-DD',
  format: {
    timestamp: 'YYYY-MM-DD HH:mm:ss:ms',
    colorize: config.NODE_ENV === 'development',
    json: config.NODE_ENV === 'production',
  },
  transports: {
    console: {
      enabled: true,
      level: config.NODE_ENV === 'development' ? 'debug' : 'info',
    },
    file: {
      enabled: config.NODE_ENV === 'production',
      errorFile: 'logs/error.log',
      combinedFile: 'logs/combined.log',
    },
  },
} as const;

// Application configuration
export const appConfig = {
  name: 'Sari-Sari Store Management System',
  version: '1.0.0',
  description: 'Professional Sari-Sari Store Management System',
  author: 'Professional Developer',
  port: config.PORT,
  env: config.NODE_ENV,
  isDevelopment: config.NODE_ENV === 'development',
  isProduction: config.NODE_ENV === 'production',
  isTest: config.NODE_ENV === 'test',
} as const;

// Feature flags
export const featureFlags = {
  enableAuth: false, // Will be enabled when authentication is implemented
  enableFileUpload: true,
  enableCaching: true,
  enableRateLimit: true,
  enableLogging: true,
  enableMetrics: config.NODE_ENV === 'production',
  enableDebugMode: config.NODE_ENV === 'development',
} as const;

// Business logic configuration
export const businessConfig = {
  inventory: {
    lowStockThreshold: 5,
    outOfStockThreshold: 0,
    maxStockQuantity: 999999,
    maxPrice: 999999.99,
  },
  debt: {
    maxDebtAmount: 999999.99,
    maxQuantity: 999999,
    defaultCurrency: 'PHP',
  },
  categories: [
    'snacks',
    'canned goods',
    'beverages',
    'personal care',
    'household',
    'condiments',
    'instant foods',
    'dairy',
    'frozen',
    'others',
  ] as const,
} as const;

// Validation rules
export const validationRules = {
  product: {
    nameMaxLength: 100,
    netWeightMaxLength: 50,
    minPrice: 0,
    maxPrice: businessConfig.inventory.maxPrice,
    minStock: 0,
    maxStock: businessConfig.inventory.maxStockQuantity,
  },
  debt: {
    customerNameMaxLength: 100,
    productNameMaxLength: 100,
    notesMaxLength: 500,
    minPrice: 0.01,
    maxPrice: businessConfig.debt.maxDebtAmount,
    minQuantity: 1,
    maxQuantity: businessConfig.debt.maxQuantity,
  },
  pagination: {
    defaultPage: 1,
    defaultLimit: apiConfig.pagination.defaultLimit,
    maxLimit: apiConfig.pagination.maxLimit,
  },
} as const;

// Export all configurations
export default {
  app: appConfig,
  db: dbConfig,
  jwt: jwtConfig,
  security: securityConfig,
  upload: uploadConfig,
  cache: cacheConfig,
  api: apiConfig,
  logging: loggingConfig,
  features: featureFlags,
  business: businessConfig,
  validation: validationRules,
} as const;

// Type exports
export type AppConfig = typeof appConfig;
export type DatabaseConfig = typeof dbConfig;
export type JWTConfig = typeof jwtConfig;
export type SecurityConfig = typeof securityConfig;
export type UploadConfig = typeof uploadConfig;
export type CacheConfig = typeof cacheConfig;
export type APIConfig = typeof apiConfig;
export type LoggingConfig = typeof loggingConfig;
export type FeatureFlags = typeof featureFlags;
export type BusinessConfig = typeof businessConfig;
export type ValidationRules = typeof validationRules;
