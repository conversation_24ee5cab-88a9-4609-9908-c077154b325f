'use client';

import React, { memo, useCallback } from 'react';
import { Edit, Trash2, AlertTriangle } from 'lucide-react';
import Image from 'next/image';
import { Product } from '@/lib/types';

interface ProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
}

const ProductCard = memo(function ProductCard({
  product,
  onEdit,
  onDelete,
}: ProductCardProps) {
  const isLowStock = product.stockQuantity <= 5;

  const handleEdit = useCallback(() => {
    onEdit(product);
  }, [onEdit, product]);

  const handleDelete = useCallback(() => {
    if (product._id) {
      onDelete(product._id);
    }
  }, [onDelete, product._id]);

  return (
    <div className='overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md'>
      {/* Product Image */}
      <div className='relative h-48 bg-gray-100'>
        {product.image ? (
          <Image
            src={product.image}
            alt={product.name}
            fill
            className='object-cover'
          />
        ) : (
          <div className='flex h-full w-full items-center justify-center text-gray-400'>
            <div className='text-center'>
              <div className='mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-lg bg-gray-200'>
                📦
              </div>
              <span className='text-sm'>No Image</span>
            </div>
          </div>
        )}

        {/* Low Stock Badge */}
        {isLowStock && (
          <div className='absolute right-2 top-2 flex items-center gap-1 rounded-full bg-red-500 px-2 py-1 text-xs text-white'>
            <AlertTriangle className='h-3 w-3' />
            Low Stock
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className='p-4'>
        <div className='mb-3'>
          <h3 className='mb-1 line-clamp-2 font-semibold text-gray-900'>
            {product.name}
          </h3>
          <p className='text-sm text-gray-600'>{product.netWeight}</p>
        </div>

        <div className='mb-3'>
          <div className='mb-1 flex items-center justify-between'>
            <span className='text-lg font-bold text-green-600'>
              ₱{product.price.toFixed(2)}
            </span>
            <span
              className={`rounded-full px-2 py-1 text-sm ${
                isLowStock
                  ? 'bg-red-100 text-red-800'
                  : product.stockQuantity === 0
                    ? 'bg-gray-100 text-gray-800'
                    : 'bg-green-100 text-green-800'
              }`}
            >
              {product.stockQuantity} in stock
            </span>
          </div>

          <div className='text-xs capitalize text-gray-500'>
            {product.category}
          </div>
        </div>

        {/* Actions */}
        <div className='flex gap-2'>
          <button
            onClick={handleEdit}
            className='flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100'
          >
            <Edit className='h-4 w-4' />
            Edit
          </button>
          <button
            onClick={handleDelete}
            className='flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100'
          >
            <Trash2 className='h-4 w-4' />
            Delete
          </button>
        </div>
      </div>
    </div>
  );
});

export default ProductCard;
