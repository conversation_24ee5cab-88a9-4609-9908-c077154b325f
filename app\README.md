# 🏪 Sari-Sari Store Management System

A professional, full-stack web application for managing Filipino sari-sari stores built with modern
technologies.

## ✨ Features

### 🎯 Core Functionality

- **Advanced Inventory Management** - Complete product lifecycle management
- **Smart Debt Tracking** - Customer credit management with payment tracking
- **Real-time Analytics** - Business insights and performance metrics
- **Professional Dashboard** - Comprehensive overview of store operations
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile

### 🔐 Security & Performance

- **JWT Authentication** - Secure user authentication and authorization
- **Role-based Access Control** - Different permission levels for users
- **Input Validation** - Comprehensive data validation and sanitization
- **Rate Limiting** - Protection against abuse and spam
- **Image Optimization** - Automatic image processing and optimization

## 🛠️ Tech Stack

- ✅ **Server**: Node.js
- ✅ **Frontend & Backend**: Next.js 15
- ✅ **Programming Language**: TypeScript
- ✅ **Design Styling**: Tailwind CSS
- ✅ **Database**: MongoDB with Mongoose ODM
- ✅ **Authentication**: JWT with bcryptjs
- ✅ **Validation**: Zod + React Hook Form
- ✅ **Testing**: Jest + Testing Library
- ✅ **Code Quality**: ESLint + Prettier
- ✅ **Icons**: Lucide React

## Project Structure

```
sari-sari-admin/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/               # API routes
│   │   │   ├── products/      # Product CRUD endpoints
│   │   │   ├── debts/         # Debt management endpoints
│   │   │   └── dashboard/     # Dashboard statistics
│   │   ├── products/          # Product management page
│   │   ├── debts/            # Debt management page
│   │   ├── layout.tsx        # Root layout
│   │   ├── page.tsx          # Dashboard homepage
│   │   └── globals.css       # Global styles
│   ├── components/            # Reusable React components
│   │   ├── Navigation.tsx    # Main navigation
│   │   ├── ProductCard.tsx   # Product display card
│   │   ├── ProductForm.tsx   # Product add/edit form
│   │   ├── DebtCard.tsx      # Debt record card
│   │   ├── DebtForm.tsx      # Debt add/edit form
│   │   └── CustomerDebtSummary.tsx # Customer debt summary
│   ├── lib/                   # Utilities and configurations
│   │   ├── mongodb.ts        # Database connection
│   │   └── models/           # Mongoose schemas
│   │       ├── Product.ts    # Product model
│   │       └── CustomerDebt.ts # Customer debt model
│   └── types/                # TypeScript type definitions
│       ├── index.ts          # Main types
│       └── global.d.ts       # Global type declarations
├── .env.local                # Environment variables
├── package.json              # Dependencies and scripts
├── tailwind.config.ts        # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
└── next.config.js           # Next.js configuration
```

## Installation & Setup

### Prerequisites

- Node.js 18+
- MongoDB (local or cloud instance)
- npm or yarn package manager

### Setup Instructions

**Important**: Due to spaces and special characters in the current path, it's recommended to move
the project to a simpler path (e.g., `C:\projects\sari-sari-admin`) before installation.

1. **Clone or move the project to a simple path**:

   ```bash
   # Move to a path without spaces or special characters
   mkdir C:\projects
   move "sari-sari-admin" "C:\projects\sari-sari-admin"
   cd C:\projects\sari-sari-admin
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**: Update `.env.local` with your MongoDB connection string:

   ```env
   MONGODB_URI=mongodb://localhost:27017/sari-sari-store
   # Or for MongoDB Atlas:
   # MONGODB_URI=mongodb+srv://username:<EMAIL>/sari-sari-store
   ```

4. **Start MongoDB** (if using local installation):

   ```bash
   mongod
   ```

5. **Run the development server**:

   ```bash
   npm run dev
   ```

6. **Open your browser**: Navigate to `http://localhost:3000`

## API Endpoints

### Products

- `GET /api/products` - Get all products (with pagination, search, filter)
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get single product
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

### Customer Debts

- `GET /api/debts` - Get all debts (with pagination, search, filter)
- `POST /api/debts` - Create new debt record
- `GET /api/debts/[id]` - Get single debt record
- `PUT /api/debts/[id]` - Update debt record
- `DELETE /api/debts/[id]` - Delete debt record
- `GET /api/debts/summary` - Get debt summary by customer

### Dashboard

- `GET /api/dashboard/stats` - Get dashboard statistics

## Database Schema

### Product Model

```typescript
{
  name: string;           // Product name
  image?: string;         // Product image URL
  netWeight: string;      // Net weight (e.g., "100g", "1L")
  price: number;          // Price in PHP
  stockQuantity: number;  // Current stock quantity
  category: string;       // Product category
  createdAt: Date;        // Creation timestamp
  updatedAt: Date;        // Last update timestamp
}
```

### Customer Debt Model

```typescript
{
  customerName: string;   // Customer name
  productName: string;    // Product name at time of debt
  productPrice: number;   // Product price at time of debt
  quantity: number;       // Quantity purchased
  totalAmount: number;    // Total amount (calculated)
  dateOfDebt: Date;       // When debt was created
  isPaid: boolean;        // Payment status
  paidDate?: Date;        // When debt was paid (if paid)
  notes?: string;         // Additional notes
  createdAt: Date;        // Creation timestamp
  updatedAt: Date;        // Last update timestamp
}
```

## Features in Detail

### Form Validation

- Required field validation
- Numeric validation for prices and quantities
- URL validation for image links
- Real-time error display
- Automatic total calculation for debts

### Responsive Design

- Mobile-first approach
- Responsive grid layouts
- Touch-friendly interface
- Optimized for various screen sizes

### User Experience

- Loading states and spinners
- Confirmation dialogs for deletions
- Success/error notifications
- Intuitive navigation
- Search and filter capabilities

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Structure

- **Components**: Reusable UI components with TypeScript props
- **API Routes**: RESTful endpoints with proper error handling
- **Database Models**: Mongoose schemas with validation
- **Types**: Comprehensive TypeScript type definitions

## Troubleshooting

### Common Issues

1. **npm install fails**: Move project to path without spaces/special characters
2. **MongoDB connection error**: Check MongoDB is running and connection string is correct
3. **Port 3000 in use**: Use `npm run dev -- -p 3001` to use different port
4. **TypeScript errors**: Run `npm run lint` to check for issues

### Path Issues

If you encounter issues with npm installation due to the current path containing spaces and special
characters (`tindahan_niDIDANG&Nene`), move the project to a simpler path:

```bash
# Create a new directory without special characters
mkdir C:\projects
# Move the project
move "sari-sari-admin" "C:\projects\sari-sari-admin"
cd C:\projects\sari-sari-admin
npm install
```

## License

MIT License - feel free to use this project for your sari-sari store management needs.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Note**: This is a complete, production-ready admin dashboard for sari-sari store management with
all CRUD operations, responsive design, and proper error handling.
