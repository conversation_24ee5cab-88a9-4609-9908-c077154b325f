import { z } from 'zod';

// Common validation schemas
export const objectIdSchema = z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format');

export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});

export const searchSchema = z.object({
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Product validation schemas
export const productCategorySchema = z.enum([
  'snacks',
  'canned goods',
  'beverages',
  'personal care',
  'household',
  'condiments',
  'instant foods',
  'dairy',
  'frozen',
  'others',
]);

export const createProductSchema = z.object({
  name: z
    .string()
    .min(1, 'Product name is required')
    .max(100, 'Product name cannot exceed 100 characters')
    .trim(),
  image: z.string().url('Invalid image URL').optional().or(z.literal('')),
  netWeight: z
    .string()
    .min(1, 'Net weight is required')
    .max(50, 'Net weight cannot exceed 50 characters')
    .trim(),
  price: z
    .number()
    .min(0, 'Price cannot be negative')
    .max(999999.99, 'Price is too high')
    .multipleOf(0.01, 'Price must have at most 2 decimal places'),
  stockQuantity: z
    .number()
    .int('Stock quantity must be an integer')
    .min(0, 'Stock quantity cannot be negative')
    .max(999999, 'Stock quantity is too high'),
  category: productCategorySchema,
});

export const updateProductSchema = createProductSchema.partial().extend({
  id: objectIdSchema,
});

export const productQuerySchema = paginationSchema.extend({
  category: productCategorySchema.optional(),
  search: z.string().optional(),
  lowStock: z.coerce.boolean().optional(),
  outOfStock: z.coerce.boolean().optional(),
});

// Customer Debt validation schemas
export const createDebtSchema = z.object({
  customerName: z
    .string()
    .min(1, 'Customer name is required')
    .max(100, 'Customer name cannot exceed 100 characters')
    .trim(),
  productName: z
    .string()
    .min(1, 'Product name is required')
    .max(100, 'Product name cannot exceed 100 characters')
    .trim(),
  productPrice: z
    .number()
    .min(0.01, 'Product price must be greater than 0')
    .max(999999.99, 'Product price is too high')
    .multipleOf(0.01, 'Product price must have at most 2 decimal places'),
  quantity: z
    .number()
    .int('Quantity must be an integer')
    .min(1, 'Quantity must be at least 1')
    .max(999999, 'Quantity is too high'),
  dateOfDebt: z
    .string()
    .datetime('Invalid date format')
    .optional()
    .or(z.date())
    .transform((val) => {
      if (typeof val === 'string') {
        return new Date(val);
      }
      return val || new Date();
    }),
  notes: z
    .string()
    .max(500, 'Notes cannot exceed 500 characters')
    .optional()
    .default(''),
});

export const updateDebtSchema = createDebtSchema.partial().extend({
  id: objectIdSchema,
  isPaid: z.boolean().optional(),
  datePaid: z
    .string()
    .datetime('Invalid date format')
    .optional()
    .or(z.date())
    .transform((val) => {
      if (typeof val === 'string') {
        return new Date(val);
      }
      return val;
    }),
});

export const debtQuerySchema = paginationSchema.extend({
  customer: z.string().optional(),
  isPaid: z.coerce.boolean().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
});

// API Response schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
  error: z.string().optional(),
  pagination: z
    .object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      pages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    })
    .optional(),
});

// File upload schemas
export const fileUploadSchema = z.object({
  file: z.any().refine((file) => {
    if (!file) return false;
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    return (
      allowedTypes.includes(file.type || file.mimetype) &&
      (file.size || file.buffer?.length || 0) <= maxSize
    );
  }, 'Invalid file type or size. Only JPEG, PNG, WebP files under 5MB are allowed.'),
});

// Environment variables schema
export const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3000),
  MONGODB_URI: z.string().min(1, 'MongoDB URI is required'),
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT refresh secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('1h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  BCRYPT_ROUNDS: z.coerce.number().min(10).max(15).default(12),
  MAX_FILE_SIZE: z.coerce.number().default(5242880),
  ALLOWED_FILE_TYPES: z.string().default('jpeg,jpg,png,gif,webp'),
  UPLOAD_DIR: z.string().default('uploads'),
  RATE_LIMIT_WINDOW_MS: z.coerce.number().default(900000),
  RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(100),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'http', 'debug']).default('info'),
});

// User authentication schemas (for future implementation)
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export const registerSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .trim(),
  email: z.string().email('Invalid email format'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password cannot exceed 128 characters')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'New password must be at least 8 characters')
    .max(128, 'New password cannot exceed 128 characters')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmNewPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "New passwords don't match",
  path: ['confirmNewPassword'],
});

// Validation helper functions
export function validateObjectId(id: string): boolean {
  return objectIdSchema.safeParse(id).success;
}

export function validatePagination(query: any): z.infer<typeof paginationSchema> {
  return paginationSchema.parse(query);
}

export function validateProductData(data: any): z.infer<typeof createProductSchema> {
  return createProductSchema.parse(data);
}

export function validateDebtData(data: any): z.infer<typeof createDebtSchema> {
  return createDebtSchema.parse(data);
}

// Type exports for TypeScript
export type CreateProductInput = z.infer<typeof createProductSchema>;
export type UpdateProductInput = z.infer<typeof updateProductSchema>;
export type ProductQuery = z.infer<typeof productQuerySchema>;
export type CreateDebtInput = z.infer<typeof createDebtSchema>;
export type UpdateDebtInput = z.infer<typeof updateDebtSchema>;
export type DebtQuery = z.infer<typeof debtQuerySchema>;
export type PaginationQuery = z.infer<typeof paginationSchema>;
export type ApiResponse = z.infer<typeof apiResponseSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type EnvConfig = z.infer<typeof envSchema>;
