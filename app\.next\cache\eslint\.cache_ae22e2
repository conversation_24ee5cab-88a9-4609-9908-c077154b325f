[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\types\\index.ts": "20"}, {"size": 3219, "mtime": 1750594136880, "results": "21", "hashOfConfig": "22"}, {"size": 2865, "mtime": 1750593938209, "results": "23", "hashOfConfig": "22"}, {"size": 3095, "mtime": 1750593829282, "results": "24", "hashOfConfig": "22"}, {"size": 3902, "mtime": 1750591102920, "results": "25", "hashOfConfig": "22"}, {"size": 3041, "mtime": 1750600513982, "results": "26", "hashOfConfig": "22"}, {"size": 4026, "mtime": 1750591103175, "results": "27", "hashOfConfig": "22"}, {"size": 14244, "mtime": 1750593736287, "results": "28", "hashOfConfig": "22"}, {"size": 618, "mtime": 1750591135161, "results": "29", "hashOfConfig": "22"}, {"size": 12465, "mtime": 1750591296018, "results": "30", "hashOfConfig": "22"}, {"size": 8148, "mtime": 1750591253140, "results": "31", "hashOfConfig": "22"}, {"size": 7994, "mtime": 1750591440807, "results": "32", "hashOfConfig": "22"}, {"size": 5050, "mtime": 1750591103951, "results": "33", "hashOfConfig": "22"}, {"size": 11196, "mtime": 1750593736856, "results": "34", "hashOfConfig": "22"}, {"size": 1886, "mtime": 1750591350768, "results": "35", "hashOfConfig": "22"}, {"size": 3342, "mtime": 1750594125145, "results": "36", "hashOfConfig": "22"}, {"size": 9651, "mtime": 1750591337417, "results": "37", "hashOfConfig": "22"}, {"size": 3519, "mtime": 1750600513985, "results": "38", "hashOfConfig": "22"}, {"size": 2256, "mtime": 1750600513982, "results": "39", "hashOfConfig": "22"}, {"size": 1072, "mtime": 1750600513954, "results": "40", "hashOfConfig": "22"}, {"size": 1247, "mtime": 1750591104428, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1rx7vx7", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts", ["103", "104"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts", ["105", "106"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts", ["107", "108", "109"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts", ["110", "111"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts", ["112", "113", "114"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx", ["115", "116", "117", "118", "119", "120", "121", "122", "123", "124"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx", ["125"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx", ["126", "127", "128", "129", "130", "131"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx", ["132", "133", "134"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx", ["135", "136", "137"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx", ["138", "139"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx", ["140", "141", "142"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts", ["143"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\types\\index.ts", [], [], {"ruleId": "144", "severity": 1, "message": "145", "line": 124, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 124, "endColumn": 18, "suggestions": "148"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 48, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 48, "endColumn": 18, "suggestions": "149"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 107, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 107, "endColumn": 18, "suggestions": "150"}, {"ruleId": "151", "severity": 1, "message": "152", "line": 15, "column": 76, "nodeType": null, "messageId": "153", "endLine": 15, "endColumn": 88, "fix": "154"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 106, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 106, "endColumn": 18, "suggestions": "155"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 35, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 35, "endColumn": 18, "suggestions": "156"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 119, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 119, "endColumn": 18, "suggestions": "157"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 156, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 156, "endColumn": 18, "suggestions": "158"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 48, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 48, "endColumn": 18, "suggestions": "159"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 113, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 113, "endColumn": 18, "suggestions": "160"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 35, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 35, "endColumn": 18, "suggestions": "161"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 121, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 121, "endColumn": 18, "suggestions": "162"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 158, "column": 5, "nodeType": "146", "messageId": "147", "endLine": 158, "endColumn": 18, "suggestions": "163"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 58, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 58, "endColumn": 20, "suggestions": "164"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 74, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 74, "endColumn": 20, "suggestions": "165"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 84, "column": 6, "nodeType": "168", "endLine": 84, "endColumn": 55, "suggestions": "169"}, {"ruleId": "170", "severity": 1, "message": "171", "line": 97, "column": 10, "nodeType": "172", "messageId": "147", "endLine": 97, "endColumn": 70}, {"ruleId": "170", "severity": 1, "message": "173", "line": 111, "column": 9, "nodeType": "172", "messageId": "147", "endLine": 111, "endColumn": 46}, {"ruleId": "144", "severity": 1, "message": "145", "line": 114, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 114, "endColumn": 20, "suggestions": "174"}, {"ruleId": "170", "severity": 1, "message": "173", "line": 115, "column": 7, "nodeType": "172", "messageId": "147", "endLine": 115, "endColumn": 42}, {"ruleId": "170", "severity": 1, "message": "173", "line": 139, "column": 9, "nodeType": "172", "messageId": "147", "endLine": 139, "endColumn": 49}, {"ruleId": "144", "severity": 1, "message": "145", "line": 142, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 142, "endColumn": 20, "suggestions": "175"}, {"ruleId": "170", "severity": 1, "message": "173", "line": 143, "column": 7, "nodeType": "172", "messageId": "147", "endLine": 143, "endColumn": 45}, {"ruleId": "144", "severity": 1, "message": "145", "line": 67, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 67, "endColumn": 20, "suggestions": "176"}, {"ruleId": "144", "severity": 1, "message": "145", "line": 38, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 38, "endColumn": 20, "suggestions": "177"}, {"ruleId": "166", "severity": 1, "message": "178", "line": 46, "column": 6, "nodeType": "168", "endLine": 46, "endColumn": 49, "suggestions": "179"}, {"ruleId": "170", "severity": 1, "message": "171", "line": 59, "column": 10, "nodeType": "172", "messageId": "147", "endLine": 59, "endColumn": 66}, {"ruleId": "170", "severity": 1, "message": "173", "line": 69, "column": 9, "nodeType": "172", "messageId": "147", "endLine": 69, "endColumn": 42}, {"ruleId": "144", "severity": 1, "message": "145", "line": 72, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 72, "endColumn": 20, "suggestions": "180"}, {"ruleId": "170", "severity": 1, "message": "173", "line": 73, "column": 7, "nodeType": "172", "messageId": "147", "endLine": 73, "endColumn": 38}, {"ruleId": "181", "severity": 1, "message": "182", "line": 16, "column": 12, "nodeType": "183", "messageId": "184", "endLine": 16, "endColumn": 30}, {"ruleId": "181", "severity": 1, "message": "185", "line": 17, "column": 14, "nodeType": "183", "messageId": "184", "endLine": 17, "endColumn": 28}, {"ruleId": "181", "severity": 1, "message": "182", "line": 18, "column": 21, "nodeType": "183", "messageId": "184", "endLine": 18, "endColumn": 39}, {"ruleId": "170", "severity": 1, "message": "173", "line": 108, "column": 9, "nodeType": "172", "messageId": "147", "endLine": 108, "endColumn": 58}, {"ruleId": "144", "severity": 1, "message": "145", "line": 111, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 111, "endColumn": 20, "suggestions": "186"}, {"ruleId": "170", "severity": 1, "message": "173", "line": 112, "column": 7, "nodeType": "172", "messageId": "147", "endLine": 112, "endColumn": 40}, {"ruleId": "181", "severity": 1, "message": "187", "line": 9, "column": 12, "nodeType": "183", "messageId": "184", "endLine": 9, "endColumn": 28}, {"ruleId": "181", "severity": 1, "message": "188", "line": 10, "column": 14, "nodeType": "183", "messageId": "184", "endLine": 10, "endColumn": 31}, {"ruleId": "170", "severity": 1, "message": "173", "line": 96, "column": 9, "nodeType": "172", "messageId": "147", "endLine": 96, "endColumn": 54}, {"ruleId": "144", "severity": 1, "message": "145", "line": 99, "column": 7, "nodeType": "146", "messageId": "147", "endLine": 99, "endColumn": 20, "suggestions": "189"}, {"ruleId": "170", "severity": 1, "message": "173", "line": 100, "column": 7, "nodeType": "172", "messageId": "147", "endLine": 100, "endColumn": 36}, {"ruleId": "181", "severity": 1, "message": "190", "line": 4, "column": 7, "nodeType": "183", "messageId": "184", "endLine": 7, "endColumn": 4}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["191"], ["192"], ["193"], "prettier/prettier", "Replace `customerName` with `⏎········customerName⏎······`", "replace", {"range": "194", "text": "195"}, ["196"], ["197"], ["198"], ["199"], ["200"], ["201"], ["202"], ["203"], ["204"], ["205"], ["206"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDebts'. Either include it or remove the dependency array.", "ArrayExpression", ["207"], "no-alert", "Unexpected confirm.", "CallExpression", "Unexpected alert.", ["208"], ["209"], ["210"], ["211"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["212"], ["213"], "no-unused-vars", "'debt' is defined but never used.", "Identifier", "unusedVar", "'debtId' is defined but never used.", ["214"], "'product' is defined but never used.", "'productId' is defined but never used.", ["215"], "'mongoose' is defined but never used.", {"messageId": "216", "data": "217", "fix": "218", "desc": "219"}, {"messageId": "216", "data": "220", "fix": "221", "desc": "219"}, {"messageId": "216", "data": "222", "fix": "223", "desc": "219"}, [547, 559], "\n        customerName\n      ", {"messageId": "216", "data": "224", "fix": "225", "desc": "219"}, {"messageId": "216", "data": "226", "fix": "227", "desc": "219"}, {"messageId": "216", "data": "228", "fix": "229", "desc": "219"}, {"messageId": "216", "data": "230", "fix": "231", "desc": "219"}, {"messageId": "216", "data": "232", "fix": "233", "desc": "219"}, {"messageId": "216", "data": "234", "fix": "235", "desc": "219"}, {"messageId": "216", "data": "236", "fix": "237", "desc": "219"}, {"messageId": "216", "data": "238", "fix": "239", "desc": "219"}, {"messageId": "216", "data": "240", "fix": "241", "desc": "219"}, {"messageId": "216", "data": "242", "fix": "243", "desc": "219"}, {"messageId": "216", "data": "244", "fix": "245", "desc": "219"}, {"desc": "246", "fix": "247"}, {"messageId": "216", "data": "248", "fix": "249", "desc": "219"}, {"messageId": "216", "data": "250", "fix": "251", "desc": "219"}, {"messageId": "216", "data": "252", "fix": "253", "desc": "219"}, {"messageId": "216", "data": "254", "fix": "255", "desc": "219"}, {"desc": "256", "fix": "257"}, {"messageId": "216", "data": "258", "fix": "259", "desc": "219"}, {"messageId": "216", "data": "260", "fix": "261", "desc": "219"}, {"messageId": "216", "data": "262", "fix": "263", "desc": "219"}, "removeConsole", {"propertyName": "264"}, {"range": "265", "text": "266"}, "Remove the console.error().", {"propertyName": "264"}, {"range": "267", "text": "266"}, {"propertyName": "264"}, {"range": "268", "text": "266"}, {"propertyName": "264"}, {"range": "269", "text": "266"}, {"propertyName": "264"}, {"range": "270", "text": "266"}, {"propertyName": "264"}, {"range": "271", "text": "266"}, {"propertyName": "264"}, {"range": "272", "text": "266"}, {"propertyName": "264"}, {"range": "273", "text": "266"}, {"propertyName": "264"}, {"range": "274", "text": "266"}, {"propertyName": "264"}, {"range": "275", "text": "266"}, {"propertyName": "264"}, {"range": "276", "text": "266"}, {"propertyName": "264"}, {"range": "277", "text": "266"}, {"propertyName": "264"}, {"range": "278", "text": "266"}, {"propertyName": "264"}, {"range": "279", "text": "266"}, "Update the dependencies array to be: [viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", {"range": "280", "text": "281"}, {"propertyName": "264"}, {"range": "282", "text": "266"}, {"propertyName": "264"}, {"range": "283", "text": "266"}, {"propertyName": "264"}, {"range": "284", "text": "266"}, {"propertyName": "264"}, {"range": "285", "text": "266"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm, selectedCategory]", {"range": "286", "text": "287"}, {"propertyName": "264"}, {"range": "288", "text": "266"}, {"propertyName": "264"}, {"range": "289", "text": "266"}, {"propertyName": "264"}, {"range": "290", "text": "266"}, "error", [3024, 3080], "", [1283, 1329], [2696, 2741], [2911, 2964], [838, 883], [2872, 2917], [3733, 3778], [1249, 1298], [2866, 2914], [844, 892], [2968, 3016], [3851, 3899], [1843, 1889], [2265, 2318], [2452, 2501], "[viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", [3180, 3225], [3862, 3917], [1406, 1462], [1397, 1446], [1546, 1589], "[currentPage, fetchProducts, searchTerm, selectedCategory]", [2194, 2242], [3003, 3046], [2482, 2528]]