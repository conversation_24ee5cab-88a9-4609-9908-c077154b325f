[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts": "20"}, {"size": 3219, "mtime": 1750602727908, "results": "21", "hashOfConfig": "22"}, {"size": 2865, "mtime": 1750602709015, "results": "23", "hashOfConfig": "22"}, {"size": 3095, "mtime": 1750602754833, "results": "24", "hashOfConfig": "22"}, {"size": 3902, "mtime": 1750591102920, "results": "25", "hashOfConfig": "22"}, {"size": 2704, "mtime": 1750602688986, "results": "26", "hashOfConfig": "22"}, {"size": 4026, "mtime": 1750591103175, "results": "27", "hashOfConfig": "22"}, {"size": 14248, "mtime": 1750604096687, "results": "28", "hashOfConfig": "22"}, {"size": 618, "mtime": 1750591135161, "results": "29", "hashOfConfig": "22"}, {"size": 12465, "mtime": 1750591296018, "results": "30", "hashOfConfig": "22"}, {"size": 8152, "mtime": 1750604070378, "results": "31", "hashOfConfig": "22"}, {"size": 7998, "mtime": 1750603985508, "results": "32", "hashOfConfig": "22"}, {"size": 5413, "mtime": 1750603893162, "results": "33", "hashOfConfig": "22"}, {"size": 11200, "mtime": 1750603960281, "results": "34", "hashOfConfig": "22"}, {"size": 1886, "mtime": 1750591350768, "results": "35", "hashOfConfig": "22"}, {"size": 3617, "mtime": 1750603795936, "results": "36", "hashOfConfig": "22"}, {"size": 9655, "mtime": 1750603936356, "results": "37", "hashOfConfig": "22"}, {"size": 3520, "mtime": 1750604206697, "results": "38", "hashOfConfig": "22"}, {"size": 2257, "mtime": 1750604227433, "results": "39", "hashOfConfig": "22"}, {"size": 1072, "mtime": 1750600513954, "results": "40", "hashOfConfig": "22"}, {"size": 1623, "mtime": 1750603999427, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "3jp6v3", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts", ["103", "104"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts", ["105"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts", ["106", "107", "108"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts", ["109", "110"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts", ["111", "112", "113"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx", ["114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx", ["119"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx", ["120", "121", "122"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx", ["123", "124", "125"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx", ["126"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx", ["127", "128"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx", ["129"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts", ["130"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts", [], [], {"ruleId": "131", "severity": 1, "message": "132", "line": 124, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 124, "endColumn": 18, "suggestions": "135"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 48, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 48, "endColumn": 18, "suggestions": "136"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 107, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 107, "endColumn": 18, "suggestions": "137"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 106, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 106, "endColumn": 18, "suggestions": "138"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 35, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 35, "endColumn": 18, "suggestions": "139"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 119, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 119, "endColumn": 18, "suggestions": "140"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 156, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 156, "endColumn": 18, "suggestions": "141"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 48, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 48, "endColumn": 18, "suggestions": "142"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 102, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 102, "endColumn": 18, "suggestions": "143"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 35, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 35, "endColumn": 18, "suggestions": "144"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 121, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 121, "endColumn": 18, "suggestions": "145"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 158, "column": 5, "nodeType": "133", "messageId": "134", "endLine": 158, "endColumn": 18, "suggestions": "146"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 58, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 58, "endColumn": 20, "suggestions": "147"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 74, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 74, "endColumn": 20, "suggestions": "148"}, {"ruleId": "149", "severity": 1, "message": "150", "line": 84, "column": 6, "nodeType": "151", "endLine": 84, "endColumn": 55, "suggestions": "152"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 114, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 114, "endColumn": 20, "suggestions": "153"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 142, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 142, "endColumn": 20, "suggestions": "154"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 67, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 67, "endColumn": 20, "suggestions": "155"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 38, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 38, "endColumn": 20, "suggestions": "156"}, {"ruleId": "149", "severity": 1, "message": "157", "line": 46, "column": 6, "nodeType": "151", "endLine": 46, "endColumn": 49, "suggestions": "158"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 72, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 72, "endColumn": 20, "suggestions": "159"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 17, "column": 12, "nodeType": "162", "messageId": "163", "endLine": 17, "endColumn": 30}, {"ruleId": "160", "severity": 1, "message": "164", "line": 18, "column": 14, "nodeType": "162", "messageId": "163", "endLine": 18, "endColumn": 28}, {"ruleId": "160", "severity": 1, "message": "161", "line": 19, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 19, "endColumn": 39}, {"ruleId": "131", "severity": 1, "message": "132", "line": 111, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 111, "endColumn": 20, "suggestions": "165"}, {"ruleId": "160", "severity": 1, "message": "166", "line": 10, "column": 12, "nodeType": "162", "messageId": "163", "endLine": 10, "endColumn": 28}, {"ruleId": "160", "severity": 1, "message": "167", "line": 11, "column": 14, "nodeType": "162", "messageId": "163", "endLine": 11, "endColumn": 31}, {"ruleId": "131", "severity": 1, "message": "132", "line": 99, "column": 7, "nodeType": "133", "messageId": "134", "endLine": 99, "endColumn": 20, "suggestions": "168"}, {"ruleId": "160", "severity": 1, "message": "169", "line": 4, "column": 7, "nodeType": "162", "messageId": "163", "endLine": 7, "endColumn": 4}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["170"], ["171"], ["172"], ["173"], ["174"], ["175"], ["176"], ["177"], ["178"], ["179"], ["180"], ["181"], ["182"], ["183"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDebts'. Either include it or remove the dependency array.", "ArrayExpression", ["184"], ["185"], ["186"], ["187"], ["188"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["189"], ["190"], "no-unused-vars", "'debt' is defined but never used.", "Identifier", "unusedVar", "'debtId' is defined but never used.", ["191"], "'product' is defined but never used.", "'productId' is defined but never used.", ["192"], "'mongoose' is defined but never used.", {"messageId": "193", "data": "194", "fix": "195", "desc": "196"}, {"messageId": "193", "data": "197", "fix": "198", "desc": "196"}, {"messageId": "193", "data": "199", "fix": "200", "desc": "196"}, {"messageId": "193", "data": "201", "fix": "202", "desc": "196"}, {"messageId": "193", "data": "203", "fix": "204", "desc": "196"}, {"messageId": "193", "data": "205", "fix": "206", "desc": "196"}, {"messageId": "193", "data": "207", "fix": "208", "desc": "196"}, {"messageId": "193", "data": "209", "fix": "210", "desc": "196"}, {"messageId": "193", "data": "211", "fix": "212", "desc": "196"}, {"messageId": "193", "data": "213", "fix": "214", "desc": "196"}, {"messageId": "193", "data": "215", "fix": "216", "desc": "196"}, {"messageId": "193", "data": "217", "fix": "218", "desc": "196"}, {"messageId": "193", "data": "219", "fix": "220", "desc": "196"}, {"messageId": "193", "data": "221", "fix": "222", "desc": "196"}, {"desc": "223", "fix": "224"}, {"messageId": "193", "data": "225", "fix": "226", "desc": "196"}, {"messageId": "193", "data": "227", "fix": "228", "desc": "196"}, {"messageId": "193", "data": "229", "fix": "230", "desc": "196"}, {"messageId": "193", "data": "231", "fix": "232", "desc": "196"}, {"desc": "233", "fix": "234"}, {"messageId": "193", "data": "235", "fix": "236", "desc": "196"}, {"messageId": "193", "data": "237", "fix": "238", "desc": "196"}, {"messageId": "193", "data": "239", "fix": "240", "desc": "196"}, "removeConsole", {"propertyName": "241"}, {"range": "242", "text": "243"}, "Remove the console.error().", {"propertyName": "241"}, {"range": "244", "text": "243"}, {"propertyName": "241"}, {"range": "245", "text": "243"}, {"propertyName": "241"}, {"range": "246", "text": "243"}, {"propertyName": "241"}, {"range": "247", "text": "243"}, {"propertyName": "241"}, {"range": "248", "text": "243"}, {"propertyName": "241"}, {"range": "249", "text": "243"}, {"propertyName": "241"}, {"range": "250", "text": "243"}, {"propertyName": "241"}, {"range": "251", "text": "243"}, {"propertyName": "241"}, {"range": "252", "text": "243"}, {"propertyName": "241"}, {"range": "253", "text": "243"}, {"propertyName": "241"}, {"range": "254", "text": "243"}, {"propertyName": "241"}, {"range": "255", "text": "243"}, {"propertyName": "241"}, {"range": "256", "text": "243"}, "Update the dependencies array to be: [viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", {"range": "257", "text": "258"}, {"propertyName": "241"}, {"range": "259", "text": "243"}, {"propertyName": "241"}, {"range": "260", "text": "243"}, {"propertyName": "241"}, {"range": "261", "text": "243"}, {"propertyName": "241"}, {"range": "262", "text": "243"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm, selectedCategory]", {"range": "263", "text": "264"}, {"propertyName": "241"}, {"range": "265", "text": "243"}, {"propertyName": "241"}, {"range": "266", "text": "243"}, {"propertyName": "241"}, {"range": "267", "text": "243"}, "error", [3024, 3080], "", [1283, 1329], [2696, 2741], [2911, 2964], [838, 883], [2872, 2917], [3733, 3778], [1249, 1298], [2529, 2577], [844, 892], [2968, 3016], [3851, 3899], [1847, 1893], [2269, 2322], [2456, 2505], "[viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", [3184, 3229], [3866, 3921], [1406, 1462], [1401, 1450], [1550, 1593], "[currentPage, fetchProducts, searchTerm, selectedCategory]", [2198, 2246], [3007, 3050], [2486, 2532]]