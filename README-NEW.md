# 🏪 Sari-Sari Store Management System

A comprehensive, professional-grade web application for managing Filipino sari-sari stores with advanced inventory management, debt tracking, analytics, and multi-user support.

## ✨ Features

### 🎯 Core Functionality
- **Advanced Inventory Management** - Complete product lifecycle management
- **Smart Debt Tracking** - Customer credit management with payment tracking
- **Real-time Analytics** - Business insights and performance metrics
- **Multi-user Support** - Role-based access control and user management
- **Professional API** - RESTful API with comprehensive documentation

### 📦 Inventory Management
- ✅ Product CRUD operations with advanced validation
- ✅ Multi-category organization (snacks, beverages, canned goods, etc.)
- ✅ Smart stock level tracking with automated alerts
- ✅ Image upload with automatic optimization (WebP conversion)
- ✅ Barcode and SKU support for quick identification
- ✅ Supplier information and contact management
- ✅ Expiry date tracking and notifications
- ✅ Cost price and profit margin calculations
- ✅ Advanced search and filtering capabilities
- ✅ Bulk operations and data import/export

### 💳 Debt Management
- ✅ Customer profile management with contact information
- ✅ Flexible payment tracking (partial/full payments)
- ✅ Due date management with overdue notifications
- ✅ Priority-based debt categorization
- ✅ Payment history and audit trails
- ✅ Customer credit limits and risk assessment
- ✅ Automated payment reminders
- ✅ Debt consolidation and settlement tracking

### 📊 Analytics & Reporting
- ✅ Real-time dashboard with key metrics
- ✅ Sales performance analytics
- ✅ Inventory turnover reports
- ✅ Customer behavior insights
- ✅ Profit margin analysis
- ✅ Low stock and reorder alerts
- ✅ Financial summaries and trends
- ✅ Exportable reports (PDF, Excel)

### 🔐 Security & Performance
- ✅ JWT-based authentication with refresh tokens
- ✅ Role-based authorization (Admin, Manager, Cashier, User)
- ✅ Rate limiting and security headers
- ✅ Input validation and sanitization
- ✅ Redis caching for optimal performance
- ✅ Database query optimization
- ✅ Comprehensive error handling and logging
- ✅ Health monitoring and metrics

## 🚀 Technology Stack

### Backend (Node.js/Express)
- **Node.js 18+** - Runtime environment
- **Express.js** - Web framework with security middleware
- **MongoDB 5.0+** - Document database with optimized schemas
- **Mongoose** - ODM with advanced validation and middleware
- **Redis** - Caching and session management
- **JWT** - Secure authentication with refresh tokens
- **Sharp** - Image processing and optimization
- **Winston** - Structured logging with multiple transports
- **Jest** - Comprehensive testing framework
- **ESLint + Prettier** - Code quality and formatting

### Frontend (React/Vite)
- **React 18** - Modern UI library with hooks
- **Vite** - Fast build tool and development server
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client with interceptors
- **React Query** - Server state management
- **React Hook Form** - Form handling with validation

### Admin Panel (Next.js)
- **Next.js 14** - Full-stack React framework
- **TypeScript** - Type safety throughout
- **Tailwind CSS** - Consistent styling
- **Recharts** - Data visualization and charts
- **NextAuth.js** - Authentication for Next.js

## 📁 Project Structure

```
sari-sari-store/
├── 📂 backend/                    # Express.js API Server
│   ├── 📂 docs/                   # API documentation
│   ├── 📂 models/                 # Mongoose models with validation
│   ├── 📂 routes/                 # Express routes with middleware
│   ├── 📂 src/
│   │   ├── 📂 config/             # Configuration files
│   │   ├── 📂 middleware/         # Custom middleware
│   │   └── 📂 utils/              # Utility functions
│   ├── 📂 scripts/                # Database migrations and utilities
│   ├── 📂 tests/                  # Comprehensive test suite
│   ├── 📂 uploads/                # File upload storage
│   ├── 📄 .eslintrc.js           # ESLint configuration
│   ├── 📄 .prettierrc.js         # Prettier configuration
│   ├── 📄 jest.config.js         # Jest test configuration
│   └── 📄 server.js              # Application entry point
├── 📂 frontend/                   # React Application
│   ├── 📂 src/
│   │   ├── 📂 components/         # Reusable React components
│   │   ├── 📂 pages/              # Page components
│   │   ├── 📂 services/           # API service layer
│   │   ├── 📂 hooks/              # Custom React hooks
│   │   ├── 📂 utils/              # Utility functions
│   │   └── 📂 types/              # TypeScript type definitions
│   └── 📂 public/                 # Static assets
└── 📂 sari-sari-admin/           # Next.js Admin Panel
    ├── 📂 src/
    │   ├── 📂 components/         # React components
    │   ├── 📂 pages/              # Next.js pages
    │   ├── 📂 lib/                # Utility libraries
    │   └── 📂 types/              # TypeScript definitions
    └── 📂 public/                 # Static assets
```

## 🛠️ Installation & Setup

### Prerequisites
- **Node.js 18+** - [Download](https://nodejs.org/)
- **MongoDB 5.0+** - [Download](https://www.mongodb.com/try/download/community)
- **Redis** (optional) - [Download](https://redis.io/download)
- **Git** - [Download](https://git-scm.com/)

### Quick Start

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd sari-sari-store
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Configure your .env file
   npm run migrate        # Run database migrations
   npm run seed          # Seed with sample data (optional)
   npm run dev           # Start development server
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev           # Start development server
   ```

4. **Admin Panel Setup**
   ```bash
   cd sari-sari-admin
   npm install
   npm run dev           # Start development server
   ```

## 📚 API Documentation

### Base URL
```
Development: http://localhost:5000/api
Production: https://your-domain.com/api
```

### Authentication
```bash
# Register
POST /api/auth/register

# Login
POST /api/auth/login

# Refresh Token
POST /api/auth/refresh

# Get Current User
GET /api/auth/me
```

### Products API
```bash
# Get all products (with filtering, pagination, sorting)
GET /api/products?page=1&limit=10&category=snacks&search=chips

# Get single product
GET /api/products/:id

# Create product
POST /api/products

# Update product
PUT /api/products/:id

# Delete product (soft delete)
DELETE /api/products/:id

# Update stock
PATCH /api/products/:id/stock

# Update price
PATCH /api/products/:id/price

# Get statistics
GET /api/products/inventory/stats
GET /api/products/categories/stats
GET /api/products/low-stock
GET /api/products/out-of-stock
```

### Debts API
```bash
# Get all debts (with filtering, pagination)
GET /api/debts?customerName=Maria&status=pending

# Get single debt
GET /api/debts/:id

# Create debt
POST /api/debts

# Update debt
PUT /api/debts/:id

# Mark as paid
PATCH /api/debts/:id/pay

# Get customer summary
GET /api/debts/customer/:customerName/summary
```

For complete API documentation, see [API Documentation](./backend/docs/API.md)

## 🧪 Testing

### Running Tests
```bash
cd backend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test types
npm run test:unit
npm run test:integration
```

## 🔧 Development

### Code Quality
```bash
# Linting
npm run lint
npm run lint:fix

# Code formatting
npm run format
npm run format:check

# Validate code quality
npm run validate
```

### Database Operations
```bash
# Run migrations
npm run migrate

# Analyze database performance
npm run db:analyze

# Show database statistics
npm run db:stats

# Show indexes
npm run db:indexes
```

## 🚀 Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations executed
- [ ] SSL certificates installed
- [ ] Monitoring and logging set up
- [ ] Backup strategy implemented
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Error tracking configured

## 📈 Performance Features

- **Redis Caching**: Intelligent caching strategy for frequently accessed data
- **Database Optimization**: Proper indexing and query optimization
- **Image Optimization**: Automatic WebP conversion and resizing
- **Lazy Loading**: Efficient data loading strategies
- **Pagination**: Optimized pagination for large datasets
- **Compression**: Gzip compression for API responses

## 🔒 Security Features

- **Authentication**: JWT with refresh token rotation
- **Authorization**: Role-based access control (RBAC)
- **Input Validation**: Comprehensive input sanitization
- **Rate Limiting**: API endpoint protection
- **Security Headers**: Helmet.js security middleware
- **File Upload Security**: Type validation and size limits

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with ❤️ for Filipino sari-sari store owners
- Inspired by the need for accessible, professional inventory management
- Thanks to the open-source community for the amazing tools and libraries

---

**Made with ❤️ for the Filipino entrepreneurial spirit**
