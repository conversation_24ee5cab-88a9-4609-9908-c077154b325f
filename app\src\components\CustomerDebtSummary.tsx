'use client';

import { useState } from 'react';
import {
  ChevronDown,
  ChevronUp,
  User,
  Calendar,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { DebtSummary } from '@/lib/types';

interface CustomerDebtSummaryProps {
  summary: DebtSummary;
}

export default function CustomerDebtSummary({
  summary,
}: CustomerDebtSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const paidDebts = summary.debts?.filter(debt => debt.isPaid) || [];
  const unpaidDebts = summary.debts?.filter(debt => !debt.isPaid) || [];

  return (
    <div className='overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm'>
      <div
        className='cursor-pointer p-6 transition-colors hover:bg-gray-50'
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <div className='flex items-center gap-2'>
              <User className='h-6 w-6 text-blue-600' />
              <h3 className='text-xl font-semibold text-gray-900'>
                {summary.customerName}
              </h3>
            </div>

            {summary.totalUnpaid > 0 && (
              <div className='rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800'>
                {summary.unpaidCount} unpaid
              </div>
            )}
          </div>

          <div className='flex items-center gap-4'>
            <div className='text-right'>
              <p className='text-sm text-gray-600'>Total Unpaid</p>
              <p className='text-2xl font-bold text-red-600'>
                ₱{summary.totalUnpaid.toFixed(2)}
              </p>
            </div>

            <div className='text-right'>
              <p className='text-sm text-gray-600'>Total Debt</p>
              <p className='text-lg font-semibold text-gray-900'>
                ₱{summary.totalDebt.toFixed(2)}
              </p>
            </div>

            {isExpanded ? (
              <ChevronUp className='h-5 w-5 text-gray-400' />
            ) : (
              <ChevronDown className='h-5 w-5 text-gray-400' />
            )}
          </div>
        </div>

        <div className='mt-4 grid grid-cols-2 gap-4 md:grid-cols-4'>
          <div className='rounded-lg bg-gray-50 p-3 text-center'>
            <p className='text-sm text-gray-600'>Total Records</p>
            <p className='text-lg font-semibold text-gray-900'>
              {summary.debtCount}
            </p>
          </div>

          <div className='rounded-lg bg-red-50 p-3 text-center'>
            <p className='text-sm text-red-600'>Unpaid Records</p>
            <p className='text-lg font-semibold text-red-900'>
              {summary.unpaidCount}
            </p>
          </div>

          <div className='rounded-lg bg-green-50 p-3 text-center'>
            <p className='text-sm text-green-600'>Paid Records</p>
            <p className='text-lg font-semibold text-green-900'>
              {summary.debtCount - summary.unpaidCount}
            </p>
          </div>

          <div className='rounded-lg bg-blue-50 p-3 text-center'>
            <p className='text-sm text-blue-600'>Payment Rate</p>
            <p className='text-lg font-semibold text-blue-900'>
              {summary.debtCount > 0
                ? Math.round(
                    ((summary.debtCount - summary.unpaidCount) /
                      summary.debtCount) *
                      100
                  )
                : 0}
              %
            </p>
          </div>
        </div>
      </div>

      {isExpanded && summary.debts && (
        <div className='border-t border-gray-200'>
          {/* Unpaid Debts */}
          {unpaidDebts.length > 0 && (
            <div className='bg-red-50 p-6'>
              <h4 className='mb-4 flex items-center gap-2 text-lg font-semibold text-red-900'>
                <XCircle className='h-5 w-5' />
                Unpaid Debts ({unpaidDebts.length})
              </h4>
              <div className='space-y-3'>
                {unpaidDebts.map(debt => (
                  <div
                    key={debt._id}
                    className='rounded-lg border border-red-200 bg-white p-4'
                  >
                    <div className='flex items-start justify-between'>
                      <div>
                        <h5 className='font-medium text-gray-900'>
                          {debt.productName}
                        </h5>
                        <p className='text-sm text-gray-600'>
                          {debt.quantity} × ₱{debt.productPrice.toFixed(2)}
                        </p>
                        <p className='mt-1 flex items-center gap-1 text-xs text-gray-500'>
                          <Calendar className='h-3 w-3' />
                          {formatDate(debt.dateOfDebt)}
                        </p>
                      </div>
                      <div className='text-right'>
                        <p className='text-lg font-bold text-red-600'>
                          ₱{debt.totalAmount.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    {debt.notes && (
                      <p className='mt-2 text-sm italic text-gray-600'>
                        &ldquo;{debt.notes}&rdquo;
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Paid Debts */}
          {paidDebts.length > 0 && (
            <div className='bg-green-50 p-6'>
              <h4 className='mb-4 flex items-center gap-2 text-lg font-semibold text-green-900'>
                <CheckCircle className='h-5 w-5' />
                Paid Debts ({paidDebts.length})
              </h4>
              <div className='space-y-3'>
                {paidDebts.map(debt => (
                  <div
                    key={debt._id}
                    className='rounded-lg border border-green-200 bg-white p-4'
                  >
                    <div className='flex items-start justify-between'>
                      <div>
                        <h5 className='font-medium text-gray-900'>
                          {debt.productName}
                        </h5>
                        <p className='text-sm text-gray-600'>
                          {debt.quantity} × ₱{debt.productPrice.toFixed(2)}
                        </p>
                        <div className='mt-1 flex items-center gap-4 text-xs text-gray-500'>
                          <span className='flex items-center gap-1'>
                            <Calendar className='h-3 w-3' />
                            Debt: {formatDate(debt.dateOfDebt)}
                          </span>
                          {debt.paidDate && (
                            <span className='flex items-center gap-1'>
                              <CheckCircle className='h-3 w-3' />
                              Paid: {formatDate(debt.paidDate)}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-lg font-bold text-green-600'>
                          ₱{debt.totalAmount.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    {debt.notes && (
                      <p className='mt-2 text-sm italic text-gray-600'>
                        &ldquo;{debt.notes}&rdquo;
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
