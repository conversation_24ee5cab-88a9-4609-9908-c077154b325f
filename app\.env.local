# Local Development Environment Variables
# Copy from .env.example and modify as needed

# Database Configuration
# For local MongoDB (if you have it installed)
# MONGODB_URI=mongodb://localhost:27017/sari-sari-store

# For MongoDB Atlas (cloud) - Replace with your connection string
MONGODB_URI=mongodb+srv://demo:<EMAIL>/sari-sari-store?retryWrites=true&w=majority

# Temporary demo database - you can use this for testing
# MONGODB_URI=mongodb+srv://demo:<EMAIL>/sari-sari-store
MONGODB_DB_NAME=sari-sari-store

# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=Sari-Sari Store Management System
APP_VERSION=1.0.0

# Authentication & Security
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_REFRESH_SECRET=dev-refresh-secret-change-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=10

# API Configuration
API_URL=http://localhost:3000/api
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpeg,jpg,png,gif,webp
UPLOAD_DIR=uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=debug

# Development Tools
ANALYZE=false
NEXT_TELEMETRY_DISABLED=1
