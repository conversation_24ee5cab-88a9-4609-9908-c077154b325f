(()=>{var e={};e.id=974,e.ids=[974],e.modules={228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},823:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx","default")},1692:(e,s,t)=>{Promise.resolve().then(t.bind(t,1204))},2019:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>n});var r=t(7413),a=t(5041),i=t.n(a);t(1120),t(1135);let n={title:"Sari-Sari Store Admin Dashboard",description:"Admin dashboard for managing sari-sari store inventory and customer debts"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})})})}},5825:()=>{},6246:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(687),a=t(5814),i=t.n(a),n=t(6189),l=t(7897),d=t(9080),o=t(1312);function c(){let e=(0,n.usePathname)(),s=[{href:"/",label:"Dashboard",icon:l.A,active:"/"===e},{href:"/products",label:"Products",icon:d.A,active:"/products"===e},{href:"/debts",label:"Customer Debts",icon:o.A,active:"/debts"===e}];return(0,r.jsx)("nav",{className:"border-b bg-white shadow-sm",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("span",{className:"text-sm font-bold text-white",children:"SS"})}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:"Sari-Sari Admin"})]}),(0,r.jsx)("div",{className:"flex space-x-1",children:s.map(e=>{let s=e.icon;return(0,r.jsxs)(i(),{href:e.href,className:`flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ${e.active?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"}`,children:[(0,r.jsx)(s,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.label})]},e.href)})})]})})})}},7371:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(687),a=t(3210),i=t(5814),n=t.n(i),l=t(9080),d=t(1312),o=t(9947),c=t(3928),m=t(228);let x=(0,t(8962).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var h=t(6246);function p(){let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)(!0),p=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"});return t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(h.A,{}),(0,r.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,r.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(h.A,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Dashboard Overview"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Monitor your sari-sari store performance and key metrics"})]}),(0,r.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Products"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e?.products.totalProducts||0})]})]})}),(0,r.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e?.debts.totalCustomers||0})]})]})}),(0,r.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-3 h-8 w-8 text-red-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Debts"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e?.debts.totalUnpaidDebts||0})]})]})}),(0,r.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-3 h-8 w-8 text-orange-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e?.debts.totalUnpaidAmount.toFixed(2)||"0.00"]})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-3",children:[(0,r.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsx)(n(),{href:"/products",className:"block",children:(0,r.jsxs)("div",{className:"rounded-lg border-l-4 border-blue-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,r.jsx)(l.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Product Management"})]}),(0,r.jsx)("p",{className:"mb-4 text-gray-600",children:"Manage inventory, track stock levels, and update product information."}),(0,r.jsx)("div",{className:"font-semibold text-blue-600",children:"Manage Products →"})]})}),(0,r.jsx)(n(),{href:"/debts",className:"block",children:(0,r.jsxs)("div",{className:"rounded-lg border-l-4 border-green-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Customer Debts"})]}),(0,r.jsx)("p",{className:"mb-4 text-gray-600",children:"Track customer debts (utang) and manage payment records."}),(0,r.jsx)("div",{className:"font-semibold text-green-600",children:"Manage Debts →"})]})})]}),e?.lowStockProducts&&e.lowStockProducts.length>0&&(0,r.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-2 h-6 w-6 text-red-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Low Stock Alert"})]}),(0,r.jsx)("div",{className:"space-y-3",children:e.lowStockProducts.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-red-50 p-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm capitalize text-gray-600",children:e.category})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-red-600",children:[e.stockQuantity," left"]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["₱",e.price.toFixed(2)]})]})]},e._id))}),(0,r.jsx)(n(),{href:"/products",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View all products →"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"mr-2 h-6 w-6 text-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Debts"})]}),e?.recentDebts&&e.recentDebts.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.recentDebts.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-start justify-between rounded-lg bg-gray-50 p-3",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.customerName}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:e.productName}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:p(e.dateOfDebt)})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["₱",e.totalAmount.toFixed(2)]}),(0,r.jsx)("span",{className:`rounded-full px-2 py-1 text-xs ${e.isPaid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isPaid?"Paid":"Unpaid"})]})]},e._id))}):(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No recent debt records"}),(0,r.jsx)(n(),{href:"/debts",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View all debts →"})]}),e?.topCustomers&&e.topCustomers.length>0&&(0,r.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,r.jsx)(x,{className:"mr-2 h-6 w-6 text-orange-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Debtors"})]}),(0,r.jsx)("div",{className:"space-y-3",children:e.topCustomers.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-orange-50 p-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.customerName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.debtCount," debt",e.debtCount>1?"s":""]})]}),(0,r.jsxs)("p",{className:"text-sm font-bold text-orange-600",children:["₱",e.totalUnpaid.toFixed(2)]})]},e.customerName))}),(0,r.jsx)(n(),{href:"/debts",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View debt summary →"})]})]})]})]})]})}},8124:(e,s,t)=>{Promise.resolve().then(t.bind(t,7371))},8847:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9377:()=>{},9947:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,945,940],()=>t(2019));module.exports=r})();