import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { apiLogger } from './logger';
import { generatePaginationMeta, type PaginationMeta } from './utils';

// Standard API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  errors?: Record<string, string[]>;
  pagination?: PaginationMeta;
  timestamp: string;
  requestId?: string;
}

// Error types
export class ApiError extends Error {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends ApiError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends ApiError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
    this.name = 'RateLimitError';
  }
}

// Success response helpers
export function successResponse<T>(
  data?: T,
  message?: string,
  pagination?: PaginationMeta,
  statusCode: number = 200
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    pagination,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: statusCode });
}

export function createdResponse<T>(
  data: T,
  message: string = 'Resource created successfully'
): NextResponse<ApiResponse<T>> {
  return successResponse(data, message, undefined, 201);
}

export function noContentResponse(): NextResponse {
  return new NextResponse(null, { status: 204 });
}

// Error response helpers
export function errorResponse(
  error: string | Error | ApiError,
  statusCode?: number,
  details?: any
): NextResponse<ApiResponse> {
  let message: string;
  let code: number;
  let errorDetails: any;

  if (error instanceof ApiError) {
    message = error.message;
    code = error.statusCode;
    errorDetails = error.details;
  } else if (error instanceof Error) {
    message = error.message;
    code = statusCode || 500;
    errorDetails = details;
  } else {
    message = error;
    code = statusCode || 500;
    errorDetails = details;
  }

  const response: ApiResponse = {
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
  };

  if (errorDetails) {
    response.errors = errorDetails;
  }

  // Log the error
  apiLogger.error(`API Error: ${message}`, { statusCode: code, details: errorDetails });

  return NextResponse.json(response, { status: code });
}

export function validationErrorResponse(error: ZodError): NextResponse<ApiResponse> {
  const errors: Record<string, string[]> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(err.message);
  });

  const response: ApiResponse = {
    success: false,
    error: 'Validation failed',
    errors,
    timestamp: new Date().toISOString(),
  };

  apiLogger.warn('Validation error', { errors });

  return NextResponse.json(response, { status: 400 });
}

// Database error handler
export function handleDatabaseError(error: any): NextResponse<ApiResponse> {
  apiLogger.error('Database error', error);

  // MongoDB duplicate key error
  if (error.code === 11000) {
    const field = Object.keys(error.keyPattern || {})[0] || 'field';
    return errorResponse(
      new ConflictError(`A record with this ${field} already exists`)
    );
  }

  // MongoDB validation error
  if (error.name === 'ValidationError') {
    const errors: Record<string, string[]> = {};
    Object.keys(error.errors).forEach((key) => {
      errors[key] = [error.errors[key].message];
    });
    
    return NextResponse.json({
      success: false,
      error: 'Validation failed',
      errors,
      timestamp: new Date().toISOString(),
    }, { status: 400 });
  }

  // MongoDB cast error (invalid ObjectId)
  if (error.name === 'CastError') {
    return errorResponse(new ValidationError('Invalid ID format'));
  }

  // Generic database error
  return errorResponse(
    new ApiError('Database operation failed', 500, 'DATABASE_ERROR')
  );
}

// Async error handler wrapper
export function asyncHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof ZodError) {
        return validationErrorResponse(error);
      }
      
      if (error instanceof ApiError) {
        return errorResponse(error);
      }

      // Check if it's a database error
      if (error && typeof error === 'object' && 'code' in error) {
        return handleDatabaseError(error);
      }

      // Generic error
      apiLogger.error('Unhandled error in API route', error);
      return errorResponse(
        new ApiError('Internal server error', 500, 'INTERNAL_ERROR')
      );
    }
  };
}

// Pagination helper
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<ApiResponse<T[]>> {
  const pagination = generatePaginationMeta(page, limit, total);
  return successResponse(data, message, pagination);
}

// Request validation helper
export function validateRequest<T>(
  schema: any,
  data: any
): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof ZodError) {
      throw error;
    }
    throw new ValidationError('Invalid request data');
  }
}

// CORS headers helper
export function setCorsHeaders(response: NextResponse): NextResponse {
  const origin = process.env.CORS_ORIGIN || 'http://localhost:3000';
  
  response.headers.set('Access-Control-Allow-Origin', origin);
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  
  return response;
}

// Security headers helper
export function setSecurityHeaders(response: NextResponse): NextResponse {
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  return response;
}

// Rate limiting helper (simple in-memory implementation)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + windowMs,
    });
    return true;
  }

  if (record.count >= maxRequests) {
    return false;
  }

  record.count++;
  return true;
}

// Request ID helper
export function getRequestId(request: Request): string {
  const requestId = request.headers.get('x-request-id') || 
    `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  return requestId;
}

// IP address helper
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddr || 'unknown';
}

// Method validation helper
export function validateMethod(
  request: Request,
  allowedMethods: string[]
): void {
  if (!allowedMethods.includes(request.method)) {
    throw new ApiError(
      `Method ${request.method} not allowed`,
      405,
      'METHOD_NOT_ALLOWED'
    );
  }
}
