'use client';

import {
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Calendar,
  User,
  Package,
} from 'lucide-react';
import { CustomerDebt } from '@/types';

interface DebtCardProps {
  debt: CustomerDebt;
  onEdit: (debt: CustomerDebt) => void;
  onDelete: (debtId: string) => void;
  onTogglePayment: (debt: CustomerDebt) => void;
}

export default function DebtCard({
  debt,
  onEdit,
  onDelete,
  onTogglePayment,
}: DebtCardProps) {
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getDaysSinceDebt = () => {
    const now = new Date();
    const debtDate = new Date(debt.dateOfDebt);
    const diffTime = Math.abs(now.getTime() - debtDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div
      className={`rounded-lg border-l-4 bg-white p-6 shadow-sm ${
        debt.isPaid ? 'border-green-500' : 'border-red-500'
      }`}
    >
      <div className='flex flex-col justify-between gap-4 md:flex-row md:items-center'>
        <div className='flex-1'>
          <div className='mb-3 flex items-center gap-4'>
            <div className='flex items-center gap-2'>
              <User className='h-5 w-5 text-gray-400' />
              <h3 className='text-lg font-semibold text-gray-900'>
                {debt.customerName}
              </h3>
            </div>

            <div
              className={`rounded-full px-3 py-1 text-sm font-medium ${
                debt.isPaid
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {debt.isPaid ? 'Paid' : 'Unpaid'}
            </div>
          </div>

          <div className='grid grid-cols-1 gap-4 text-sm md:grid-cols-3'>
            <div className='flex items-center gap-2'>
              <Package className='h-4 w-4 text-gray-400' />
              <div>
                <p className='text-gray-600'>Product</p>
                <p className='font-medium text-gray-900'>{debt.productName}</p>
              </div>
            </div>

            <div>
              <p className='text-gray-600'>Quantity & Price</p>
              <p className='font-medium text-gray-900'>
                {debt.quantity} × ₱{debt.productPrice.toFixed(2)}
              </p>
            </div>

            <div>
              <p className='text-gray-600'>Total Amount</p>
              <p className='text-lg font-bold text-gray-900'>
                ₱{debt.totalAmount.toFixed(2)}
              </p>
            </div>
          </div>

          <div className='mt-3 flex items-center gap-4 text-sm text-gray-600'>
            <div className='flex items-center gap-1'>
              <Calendar className='h-4 w-4' />
              <span>Debt Date: {formatDate(debt.dateOfDebt)}</span>
            </div>

            <span>•</span>

            <span>{getDaysSinceDebt()} days ago</span>

            {debt.isPaid && debt.paidDate && (
              <>
                <span>•</span>
                <span>Paid: {formatDate(debt.paidDate)}</span>
              </>
            )}
          </div>

          {debt.notes && (
            <div className='mt-3 rounded-lg bg-gray-50 p-3'>
              <p className='text-sm text-gray-700'>
                <span className='font-medium'>Notes:</span> {debt.notes}
              </p>
            </div>
          )}
        </div>

        <div className='flex w-full flex-col gap-2 md:w-auto'>
          <button
            onClick={() => onTogglePayment(debt)}
            className={`flex items-center justify-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
              debt.isPaid
                ? 'bg-red-50 text-red-600 hover:bg-red-100'
                : 'bg-green-50 text-green-600 hover:bg-green-100'
            }`}
          >
            {debt.isPaid ? (
              <>
                <XCircle className='h-4 w-4' />
                Mark Unpaid
              </>
            ) : (
              <>
                <CheckCircle className='h-4 w-4' />
                Mark Paid
              </>
            )}
          </button>

          <div className='flex gap-2'>
            <button
              onClick={() => onEdit(debt)}
              className='flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100'
            >
              <Edit className='h-4 w-4' />
              Edit
            </button>
            <button
              onClick={() => debt._id && onDelete(debt._id)}
              className='flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100'
            >
              <Trash2 className='h-4 w-4' />
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
