import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import React from 'react';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Sari-Sari Store Admin Dashboard',
  description:
    'Admin dashboard for managing sari-sari store inventory and customer debts',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <div className='min-h-screen bg-gray-50'>{children}</div>
      </body>
    </html>
  );
}
