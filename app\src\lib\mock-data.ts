// Mock data for development when MongoDB is not available

export const mockProducts = [
  {
    _id: '1',
    name: 'Lucky Me! Pancit Canton',
    netWeight: '60g',
    price: 15.00,
    stockQuantity: 50,
    category: 'instant foods',
    image: '',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    isLowStock: false,
    isOutOfStock: false,
    totalValue: 750,
    stockStatus: 'high_stock'
  },
  {
    _id: '2',
    name: 'Coca-Cola 350ml',
    netWeight: '350ml',
    price: 25.00,
    stockQuantity: 30,
    category: 'beverages',
    image: '',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    isLowStock: false,
    isOutOfStock: false,
    totalValue: 750,
    stockStatus: 'high_stock'
  },
  {
    _id: '3',
    name: 'Skyflakes Crackers',
    netWeight: '250g',
    price: 35.00,
    stockQuantity: 3,
    category: 'snacks',
    image: '',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
    isLowStock: true,
    isOutOfStock: false,
    totalValue: 105,
    stockStatus: 'low_stock'
  },
  {
    _id: '4',
    name: 'Corned Beef - Argentina',
    netWeight: '150g',
    price: 45.00,
    stockQuantity: 0,
    category: 'canned goods',
    image: '',
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-04'),
    isLowStock: false,
    isOutOfStock: true,
    totalValue: 0,
    stockStatus: 'out_of_stock'
  },
  {
    _id: '5',
    name: 'Safeguard Soap',
    netWeight: '90g',
    price: 18.00,
    stockQuantity: 25,
    category: 'personal care',
    image: '',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
    isLowStock: false,
    isOutOfStock: false,
    totalValue: 450,
    stockStatus: 'high_stock'
  }
];

export const mockDebts = [
  {
    _id: '1',
    customerName: 'Maria Santos',
    productName: 'Lucky Me! Pancit Canton',
    productPrice: 15.00,
    quantity: 2,
    totalAmount: 30.00,
    dateOfDebt: new Date('2024-01-15'),
    isPaid: false,
    paidDate: null,
    notes: 'Regular customer',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    daysSinceDebt: 7,
    isOverdue: false,
    urgencyLevel: 'recent'
  },
  {
    _id: '2',
    customerName: 'Juan Dela Cruz',
    productName: 'Coca-Cola 350ml',
    productPrice: 25.00,
    quantity: 1,
    totalAmount: 25.00,
    dateOfDebt: new Date('2024-01-10'),
    isPaid: true,
    paidDate: new Date('2024-01-20'),
    notes: '',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20'),
    daysSinceDebt: 12,
    isOverdue: false,
    urgencyLevel: 'recent'
  },
  {
    _id: '3',
    customerName: 'Ana Garcia',
    productName: 'Skyflakes Crackers',
    productPrice: 35.00,
    quantity: 3,
    totalAmount: 105.00,
    dateOfDebt: new Date('2023-12-01'),
    isPaid: false,
    paidDate: null,
    notes: 'Long-time customer',
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2023-12-01'),
    daysSinceDebt: 52,
    isOverdue: true,
    urgencyLevel: 'critical'
  }
];

export const mockDashboardStats = {
  products: {
    totalProducts: 5,
    lowStockProducts: 1,
    outOfStockProducts: 1,
    totalStockValue: 2055.00,
    totalStockQuantity: 108
  },
  debts: {
    totalDebts: 3,
    totalAmount: 160.00,
    totalUnpaidAmount: 135.00,
    totalPaidAmount: 25.00,
    unpaidCount: 2,
    paidCount: 1,
    uniqueCustomerCount: 3
  }
};

export const mockDebtSummary = [
  {
    customerName: 'Maria Santos',
    totalUnpaidAmount: 30.00,
    unpaidCount: 1
  },
  {
    customerName: 'Ana Garcia',
    totalUnpaidAmount: 105.00,
    unpaidCount: 1
  }
];

// Helper function to simulate API delay
export const simulateApiDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms));

// Mock API responses
export const mockApiResponses = {
  products: {
    success: true,
    data: mockProducts,
    pagination: {
      page: 1,
      limit: 10,
      total: mockProducts.length,
      pages: 1,
      hasNext: false,
      hasPrev: false
    }
  },
  debts: {
    success: true,
    data: mockDebts,
    pagination: {
      page: 1,
      limit: 10,
      total: mockDebts.length,
      pages: 1,
      hasNext: false,
      hasPrev: false
    }
  },
  dashboardStats: {
    success: true,
    data: mockDashboardStats
  },
  debtSummary: {
    success: true,
    data: mockDebtSummary
  }
};

// Environment check
export const isMockMode = process.env.MOCK_MODE === 'true' || 
  process.env.NODE_ENV === 'development' && !process.env.MONGODB_URI?.includes('mongodb');

export default {
  products: mockProducts,
  debts: mockDebts,
  dashboardStats: mockDashboardStats,
  debtSummary: mockDebtSummary,
  apiResponses: mockApiResponses,
  isMockMode,
  simulateApiDelay
};
