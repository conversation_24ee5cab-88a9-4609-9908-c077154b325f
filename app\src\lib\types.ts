// Core types for Sari-Sari Store

export interface Product {
  _id?: string;
  name: string;
  image?: string;
  netWeight: string;
  price: number;
  stockQuantity: number;
  category: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CustomerDebt {
  _id?: string;
  customerName: string;
  productName: string;
  productPrice: number;
  quantity: number;
  totalAmount: number;
  dateOfDebt: Date;
  isPaid: boolean;
  paidDate?: Date;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DashboardStats {
  products: {
    totalProducts: number;
    lowStockProducts: number;
    outOfStockProducts: number;
    totalStockValue: number;
  };
  debts: {
    totalDebts: number;
    totalAmount: number;
    totalUnpaidAmount: number;
    unpaidCount: number;
  };
}

export interface DebtSummary {
  customerName: string;
  totalDebt: number;
  totalUnpaid: number;
  totalUnpaidAmount: number;
  debtCount: number;
  unpaidCount: number;
  debts?: CustomerDebt[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export type ProductCategory =
  | 'snacks'
  | 'canned goods'
  | 'beverages'
  | 'personal care'
  | 'household'
  | 'condiments'
  | 'instant foods'
  | 'dairy'
  | 'frozen'
  | 'others';

export const PRODUCT_CATEGORIES: ProductCategory[] = [
  'snacks',
  'canned goods',
  'beverages',
  'personal care',
  'household',
  'condiments',
  'instant foods',
  'dairy',
  'frozen',
  'others',
];
