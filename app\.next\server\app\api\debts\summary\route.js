(()=>{var t={};t.id=861,t.ids=[861],t.modules={841:(t,e,a)=>{"use strict";a.d(e,{A:()=>i});var r=a(6037),s=a.n(r);let o=new r.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(t){return Number.isInteger(t)&&t>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({customerName:1}),o.index({isPaid:1}),o.index({dateOfDebt:-1}),o.index({customerName:1,isPaid:1}),o.virtual("daysSinceDebt").get(function(){let t=new Date,e=new Date(this.dateOfDebt);return Math.ceil(Math.abs(t.getTime()-e.getTime())/864e5)}),o.pre("save",function(t){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),t()}),o.statics.getDebtSummaryByCustomer=async function(t){let e=await this.find({customerName:t}).sort({dateOfDebt:-1});return{customerName:t,totalDebt:e.reduce((t,e)=>t+e.totalAmount,0),totalUnpaid:e.filter(t=>!t.isPaid).reduce((t,e)=>t+e.totalAmount,0),debtCount:e.length,unpaidCount:e.filter(t=>!t.isPaid).length,debts:e}};let i=s().models.CustomerDebt||s().model("CustomerDebt",o)},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(t,e,a)=>{"use strict";a.d(e,{A:()=>n});var r=a(6037),s=a.n(r);let o=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;i||(i=global.mongoose={conn:null,promise:null});let n=async function(){if(i.conn)return i.conn;i.promise||(i.promise=s().connect(o,{bufferCommands:!1}).then(()=>s().connection));try{i.conn=await i.promise}catch(t){throw i.promise=null,t}return i.conn}},6037:t=>{"use strict";t.exports=require("mongoose")},6487:()=>{},8335:()=>{},8566:(t,e,a)=>{"use strict";a.r(e),a.d(e,{patchFetch:()=>D,routeModule:()=>l,serverHooks:()=>b,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{GET:()=>m});var s=a(6559),o=a(8088),i=a(7719),n=a(2190),u=a(5745),d=a(841);async function m(t){try{await (0,u.A)();let{searchParams:e}=new URL(t.url),a=e.get("customer");if(a){let t=await d.A.getDebtSummaryByCustomer(a);return n.NextResponse.json({success:!0,data:t})}{let t=[{$group:{_id:"$customerName",totalDebt:{$sum:"$totalAmount"},totalUnpaid:{$sum:{$cond:[{$eq:["$isPaid",!1]},"$totalAmount",0]}},debtCount:{$sum:1},unpaidCount:{$sum:{$cond:[{$eq:["$isPaid",!1]},1,0]}},lastDebtDate:{$max:"$dateOfDebt"}}},{$project:{customerName:"$_id",totalDebt:1,totalUnpaid:1,debtCount:1,unpaidCount:1,lastDebtDate:1,_id:0}},{$sort:{totalUnpaid:-1}}],e=await d.A.aggregate(t),a=await d.A.aggregate([{$group:{_id:null,totalCustomers:{$addToSet:"$customerName"},totalDebts:{$sum:1},totalUnpaidDebts:{$sum:{$cond:[{$eq:["$isPaid",!1]},1,0]}},totalDebtAmount:{$sum:"$totalAmount"},totalUnpaidAmount:{$sum:{$cond:[{$eq:["$isPaid",!1]},"$totalAmount",0]}}}},{$project:{totalCustomers:{$size:"$totalCustomers"},totalDebts:1,totalUnpaidDebts:1,totalDebtAmount:1,totalUnpaidAmount:1,_id:0}}]);return n.NextResponse.json({success:!0,data:{customerSummaries:e,overallStats:a[0]||{totalCustomers:0,totalDebts:0,totalUnpaidDebts:0,totalDebtAmount:0,totalUnpaidAmount:0}}})}}catch(t){return n.NextResponse.json({success:!1,error:"Failed to fetch debt summary"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/debts/summary/route",pathname:"/api/debts/summary",filename:"route",bundlePath:"app/api/debts/summary/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:b}=l;function D(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var e=require("../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[447,580],()=>a(8566));module.exports=r})();