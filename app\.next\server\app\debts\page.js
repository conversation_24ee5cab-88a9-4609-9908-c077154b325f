(()=>{var e={};e.id=923,e.ids=[923],e.modules={228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},823:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1860:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=s(5239),r=s(8088),l=s(8170),d=s.n(l),i=s(893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c={children:["",{children:["debts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6617)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/debts/page",pathname:"/debts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(687),r=s(3210),l=s(6474),d=s(1312),i=s(228),n=s(8962);let c=(0,n.A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var o=s(3928),m=s(9270),x=s(8492),h=s(9479);let u=(0,n.A)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var p=s(8819);function b({debt:e,onSubmit:t,onCancel:s}){let[l,d]=(0,r.useState)({customerName:"",productName:"",productPrice:"",quantity:"",dateOfDebt:"",isPaid:!1,notes:""}),[i,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)({}),m=()=>{let e={};return l.customerName.trim()||(e.customerName="Customer name is required"),l.productName.trim()||(e.productName="Product name is required"),(!l.productPrice||0>=parseFloat(l.productPrice))&&(e.productPrice="Valid product price is required"),(!l.quantity||0>=parseInt(l.quantity))&&(e.quantity="Valid quantity is required"),l.dateOfDebt||(e.dateOfDebt="Date of debt is required"),o(e),0===Object.keys(e).length},x=async s=>{if(s.preventDefault(),m()){n(!0);try{let s=e?`/api/debts/${e._id}`:"/api/debts",a=e?"PUT":"POST",r=await fetch(s,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify({...l,productPrice:parseFloat(l.productPrice),quantity:parseInt(l.quantity)})}),d=await r.json();d.success?t():alert(d.error||"Failed to save debt record")}catch(e){alert("Error saving debt record")}finally{n(!1)}}},b=e=>{let{name:t,value:s,type:a}=e.target,r=e.target.checked;d(e=>({...e,[t]:"checkbox"===a?r:s})),c[t]&&o(e=>({...e,[t]:""}))};return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,a.jsxs)("div",{className:"max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between border-b p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:e?"Edit Debt Record":"Add New Debt Record"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 transition-colors hover:text-gray-600",children:(0,a.jsx)(h.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("form",{onSubmit:x,className:"space-y-4 p-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"customerName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Customer Name *"}),(0,a.jsx)("input",{type:"text",id:"customerName",name:"customerName",value:l.customerName,onChange:b,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${c.customerName?"border-red-500":"border-gray-300"}`,placeholder:"Enter customer name"}),c.customerName&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:c.customerName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"productName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Name *"}),(0,a.jsx)("input",{type:"text",id:"productName",name:"productName",value:l.productName,onChange:b,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${c.productName?"border-red-500":"border-gray-300"}`,placeholder:"Enter product name"}),c.productName&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:c.productName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"productPrice",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Price (PHP) *"}),(0,a.jsx)("input",{type:"number",id:"productPrice",name:"productPrice",value:l.productPrice,onChange:b,step:"0.01",min:"0",className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${c.productPrice?"border-red-500":"border-gray-300"}`,placeholder:"0.00"}),c.productPrice&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:c.productPrice})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"quantity",className:"mb-1 block text-sm font-medium text-gray-700",children:"Quantity *"}),(0,a.jsx)("input",{type:"number",id:"quantity",name:"quantity",value:l.quantity,onChange:b,min:"1",className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${c.quantity?"border-red-500":"border-gray-300"}`,placeholder:"1"}),c.quantity&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:c.quantity})]}),l.productPrice&&l.quantity&&(0,a.jsx)("div",{className:"rounded-lg bg-blue-50 p-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:["Total Amount: ₱",((parseFloat(l.productPrice)||0)*(parseInt(l.quantity)||0)).toFixed(2)]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"dateOfDebt",className:"mb-1 block text-sm font-medium text-gray-700",children:"Date of Debt *"}),(0,a.jsx)("input",{type:"date",id:"dateOfDebt",name:"dateOfDebt",value:l.dateOfDebt,onChange:b,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${c.dateOfDebt?"border-red-500":"border-gray-300"}`}),c.dateOfDebt&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:c.dateOfDebt})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"isPaid",name:"isPaid",checked:l.isPaid,onChange:b,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("label",{htmlFor:"isPaid",className:"ml-2 block text-sm text-gray-900",children:"Mark as paid"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"notes",className:"mb-1 block text-sm font-medium text-gray-700",children:"Notes (Optional)"}),(0,a.jsx)("textarea",{id:"notes",name:"notes",value:l.notes,onChange:b,rows:3,className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",placeholder:"Additional notes about this debt..."})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:i,className:"flex flex-1 items-center justify-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700 disabled:opacity-50",children:i?(0,a.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),e?"Update":"Create"]})})]})]})]})})}let g=(0,n.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var y=s(9080);let j=(0,n.A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var f=s(9923),N=s(8233);let v=(0,r.memo)(function({debt:e,onEdit:t,onDelete:s,onTogglePayment:l}){let d=(0,r.useCallback)(()=>{t(e)},[t,e]),n=(0,r.useCallback)(()=>{e._id&&s(e._id)},[s,e._id]),o=(0,r.useCallback)(()=>{l(e)},[l,e]),m=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,a.jsx)("div",{className:`rounded-lg border-l-4 bg-white p-6 shadow-sm ${e.isPaid?"border-green-500":"border-red-500"}`,children:(0,a.jsxs)("div",{className:"flex flex-col justify-between gap-4 md:flex-row md:items-center",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-3 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.customerName})]}),(0,a.jsx)("div",{className:`rounded-full px-3 py-1 text-sm font-medium ${e.isPaid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isPaid?"Paid":"Unpaid"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Product"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.productName})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Quantity & Price"}),(0,a.jsxs)("p",{className:"font-medium text-gray-900",children:[e.quantity," \xd7 ₱",e.productPrice.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Total Amount"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["₱",e.totalAmount.toFixed(2)]})]})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Debt Date: ",m(e.dateOfDebt)]})]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[(()=>{let t=new Date,s=new Date(e.dateOfDebt);return Math.ceil(Math.abs(t.getTime()-s.getTime())/864e5)})()," days ago"]}),e.isPaid&&e.paidDate&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Paid: ",m(e.paidDate)]})]})]}),e.notes&&(0,a.jsx)("div",{className:"mt-3 rounded-lg bg-gray-50 p-3",children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,a.jsx)("span",{className:"font-medium",children:"Notes:"})," ",e.notes]})})]}),(0,a.jsxs)("div",{className:"flex w-full flex-col gap-2 md:w-auto",children:[(0,a.jsx)("button",{onClick:o,className:`flex items-center justify-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ${e.isPaid?"bg-red-50 text-red-600 hover:bg-red-100":"bg-green-50 text-green-600 hover:bg-green-100"}`,children:e.isPaid?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c,{className:"h-4 w-4"}),"Mark Unpaid"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{className:"h-4 w-4"}),"Mark Paid"]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:d,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsxs)("button",{onClick:n,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})})}),w=(0,n.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),k=(0,n.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);function P({summary:e}){let[t,s]=(0,r.useState)(!1),l=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),d=e.debts?.filter(e=>e.isPaid)||[],n=e.debts?.filter(e=>!e.isPaid)||[];return(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm",children:[(0,a.jsxs)("div",{className:"cursor-pointer p-6 transition-colors hover:bg-gray-50",onClick:()=>s(!t),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g,{className:"h-6 w-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:e.customerName})]}),e.totalUnpaid>0&&(0,a.jsxs)("div",{className:"rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800",children:[e.unpaidCount," unpaid"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Unpaid"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["₱",e.totalUnpaid.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Debt"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["₱",e.totalDebt.toFixed(2)]})]}),t?(0,a.jsx)(w,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(k,{className:"h-5 w-5 text-gray-400"})]})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Records"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:e.debtCount})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-red-50 p-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-red-600",children:"Unpaid Records"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-red-900",children:e.unpaidCount})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-green-50 p-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Paid Records"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-green-900",children:e.debtCount-e.unpaidCount})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-blue-50 p-3 text-center",children:[(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"Payment Rate"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-blue-900",children:[e.debtCount>0?Math.round((e.debtCount-e.unpaidCount)/e.debtCount*100):0,"%"]})]})]})]}),t&&e.debts&&(0,a.jsxs)("div",{className:"border-t border-gray-200",children:[n.length>0&&(0,a.jsxs)("div",{className:"bg-red-50 p-6",children:[(0,a.jsxs)("h4",{className:"mb-4 flex items-center gap-2 text-lg font-semibold text-red-900",children:[(0,a.jsx)(c,{className:"h-5 w-5"}),"Unpaid Debts (",n.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:n.map(e=>(0,a.jsxs)("div",{className:"rounded-lg border border-red-200 bg-white p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-900",children:e.productName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," \xd7 ₱",e.productPrice.toFixed(2)]}),(0,a.jsxs)("p",{className:"mt-1 flex items-center gap-1 text-xs text-gray-500",children:[(0,a.jsx)(i.A,{className:"h-3 w-3"}),l(e.dateOfDebt)]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",e.totalAmount.toFixed(2)]})})]}),e.notes&&(0,a.jsxs)("p",{className:"mt-2 text-sm italic text-gray-600",children:["“",e.notes,"”"]})]},e._id))})]}),d.length>0&&(0,a.jsxs)("div",{className:"bg-green-50 p-6",children:[(0,a.jsxs)("h4",{className:"mb-4 flex items-center gap-2 text-lg font-semibold text-green-900",children:[(0,a.jsx)(j,{className:"h-5 w-5"}),"Paid Debts (",d.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"rounded-lg border border-green-200 bg-white p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-900",children:e.productName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," \xd7 ₱",e.productPrice.toFixed(2)]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(i.A,{className:"h-3 w-3"}),"Debt: ",l(e.dateOfDebt)]}),e.paidDate&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(j,{className:"h-3 w-3"}),"Paid: ",l(e.paidDate)]})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"text-lg font-bold text-green-600",children:["₱",e.totalAmount.toFixed(2)]})})]}),e.notes&&(0,a.jsxs)("p",{className:"mt-2 text-sm italic text-gray-600",children:["“",e.notes,"”"]})]},e._id))})]})]})]})}var D=s(6246);function A(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)([]),[h,u]=(0,r.useState)(!0),[p,g]=(0,r.useState)(!1),[y,j]=(0,r.useState)(null),[f,N]=(0,r.useState)(""),[w,k]=(0,r.useState)("all"),[A,C]=(0,r.useState)("list"),[S,q]=(0,r.useState)(1),[F,M]=(0,r.useState)(1),[U,O]=(0,r.useState)({totalCustomers:0,totalDebts:0,totalUnpaidDebts:0,totalDebtAmount:0,totalUnpaidAmount:0}),_=async()=>{try{u(!0);let e=new URLSearchParams({page:S.toString(),limit:"10",...f&&{customer:f},..."all"!==w&&{isPaid:"paid"===w?"true":"false"}}),s=await fetch(`/api/debts?${e}`),a=await s.json();a.success&&(t(a.data),M(a.pagination.pages))}catch(e){}finally{u(!1)}},E=async()=>{try{let e=await fetch("/api/debts/summary"),t=await e.json();t.success&&(n(t.data.customerSummaries),O(t.data.overallStats))}catch(e){}},T=()=>{j(null),g(!0)},L=e=>{j(e),g(!0)},$=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch(`/api/debts/${e}`,{method:"DELETE"})).ok?"list"===A?_():E():alert("Failed to delete debt record")}catch(e){alert("Error deleting debt record")}},R=async e=>{try{(await fetch(`/api/debts/${e._id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,isPaid:!e.isPaid})})).ok?"list"===A?_():E():alert("Failed to update payment status")}catch(e){alert("Error updating payment status")}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(D.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Customer Debts (Utang)"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track and manage customer debt records"})]}),(0,a.jsxs)("button",{onClick:T,className:"flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white transition-colors hover:bg-green-700",children:[(0,a.jsx)(l.A,{className:"h-5 w-5"}),"Add Debt"]})]}),(0,a.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalCustomers})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"mr-3 h-8 w-8 text-orange-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Debts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalDebts})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c,{className:"mr-3 h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Debts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalUnpaidDebts})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",U.totalUnpaidAmount.toFixed(2)]})]})]})})]}),(0,a.jsx)("div",{className:"mb-8 rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 md:flex-row md:items-center",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>C("list"),className:`rounded-lg px-4 py-2 text-sm font-medium transition-colors ${"list"===A?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"Debt List"}),(0,a.jsx)("button",{onClick:()=>C("summary"),className:`rounded-lg px-4 py-2 text-sm font-medium transition-colors ${"summary"===A?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"Customer Summary"})]}),(0,a.jsxs)("div",{className:"flex flex-1 flex-col gap-4 md:max-w-md md:flex-row",children:[(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),q(1),"list"===A?_():E()},className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search customers...",value:f,onChange:e=>N(e.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500"})]})}),"list"===A&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("select",{value:w,onChange:e=>{k(e.target.value),q(1)},className:"rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"unpaid",children:"Unpaid"}),(0,a.jsx)("option",{value:"paid",children:"Paid"})]})]})]})]})}),h?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})}):"list"===A?(0,a.jsx)(a.Fragment,{children:0===e.length?(0,a.jsxs)("div",{className:"py-12 text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No debt records found"}),(0,a.jsx)("p",{className:"mb-6 text-gray-600",children:"Start tracking customer debts"}),(0,a.jsxs)("button",{onClick:T,className:"inline-flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white hover:bg-green-700",children:[(0,a.jsx)(l.A,{className:"h-5 w-5"}),"Add Debt Record"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-8 grid gap-4",children:e.map(e=>(0,a.jsx)(v,{debt:e,onEdit:L,onDelete:$,onTogglePayment:R},e._id))}),F>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>q(e=>Math.max(e-1,1)),disabled:1===S,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),Array.from({length:F},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>q(e),className:`rounded-lg border px-4 py-2 ${S===e?"border-blue-600 bg-blue-600 text-white":"border-gray-300 hover:bg-gray-50"}`,children:e},e)),(0,a.jsx)("button",{onClick:()=>q(e=>Math.min(e+1,F)),disabled:S===F,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})}):(0,a.jsx)("div",{className:"grid gap-6",children:0===s.length?(0,a.jsxs)("div",{className:"py-12 text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No customer debt records"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Customer debt summaries will appear here"})]}):s.map(e=>(0,a.jsx)(P,{summary:e},e.customerName))})]}),p&&(0,a.jsx)(b,{debt:y,onSubmit:()=>{g(!1),j(null),"list"===A?_():E()},onCancel:()=>{g(!1),j(null)}})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>d});var a=s(7413),r=s(5041),l=s.n(r);s(1120),s(1135);let d={title:"Sari-Sari Store Admin Dashboard",description:"Admin dashboard for managing sari-sari store inventory and customer debts"};function i({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:l().className,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})})})}},5258:(e,t,s)=>{Promise.resolve().then(s.bind(s,6617))},5426:(e,t,s)=>{Promise.resolve().then(s.bind(s,3008))},5825:()=>{},6246:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(687),r=s(5814),l=s.n(r),d=s(6189),i=s(7897),n=s(9080),c=s(1312);function o(){let e=(0,d.usePathname)(),t=[{href:"/",label:"Dashboard",icon:i.A,active:"/"===e},{href:"/products",label:"Products",icon:n.A,active:"/products"===e},{href:"/debts",label:"Customer Debts",icon:c.A,active:"/debts"===e}];return(0,a.jsx)("nav",{className:"border-b bg-white shadow-sm",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsxs)(l(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600",children:(0,a.jsx)("span",{className:"text-sm font-bold text-white",children:"SS"})}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:"Sari-Sari Admin"})]}),(0,a.jsx)("div",{className:"flex space-x-1",children:t.map(e=>{let t=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:`flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ${e.active?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"}`,children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]},e.href)})})]})})})}},6474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\debts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx","default")},8233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8492:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},8847:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9377:()=>{},9479:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9923:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(8962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,945,940],()=>s(1860));module.exports=a})();